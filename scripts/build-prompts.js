#!/usr/bin/env node

import { readFileSync, writeFileSync, existsSync, mkdirSync, readdirSync } from 'fs';
import { join, dirname, basename } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

function buildPrompts() {
  try {
    // Read the system prompt
    const promptPath = join(projectRoot, 'resources/system-prompt.md');
    const promptContent = readFileSync(promptPath, 'utf-8');
    
    // Extract the actual prompt content, removing markdown headers
    const cleanPrompt = promptContent
      .replace(/# System Prompt for AI Requirements Expansion\n\n/, '')
      .replace(/## Task\n\n/, '')
      .replace(/## Guidelines\n\n/, '')
      .replace(/## Template Structure\nUse this structure for your response:\n\n/, '')
      .replace(/- /g, '• ')
      .trim();

    // Read the page relevancy prompt
    const relevancyPromptPath = join(projectRoot, 'resources/page-relevancy-prompt.md');
    const relevancyPromptContent = readFileSync(relevancyPromptPath, 'utf-8');
    
    // Extract the actual relevancy prompt content, removing markdown header
    const cleanRelevancyPrompt = relevancyPromptContent
      .replace(/# Page Relevancy Analysis Prompt\n\n/, '')
      .trim();

    // Dynamically scan for template files
    const templatesDir = join(projectRoot, 'resources/templates');
    const templateFiles = readdirSync(templatesDir)
      .filter(file => file.endsWith('-template.md'))
      .sort(); // Sort for consistent ordering

    // Read all template files
    const templates = templateFiles.map(file => {
      const templatePath = join(templatesDir, file);
      const content = readFileSync(templatePath, 'utf-8');
      const name = basename(file, '-template.md'); // e.g., 'story', 'task', 'bug'
      const constantName = `${name.toUpperCase()}_TEMPLATE`; // e.g., 'STORY_TEMPLATE'
      
      return {
        name,
        constantName,
        content,
        file
      };
    });

    // Generate template constants
    const templateConstants = templates.map(template => 
      `/**
 * The default ${template.name} template structure for the AI to follow
 * @type {string}
 */
export const ${template.constantName} = ${JSON.stringify(template.content, null, 2)};`
    ).join('\n\n');

    // Generate template array
    const templateArray = templates.map(template => 
      `  {
    name: '${template.name}',
    constantName: '${template.constantName}',
    content: ${template.constantName}
  }`
    ).join(',\n');

    // Generate switch cases for getDefaultTemplate
    const switchCases = templates.map(template => 
      `    case '${template.name}':
      return ${template.constantName};`
    ).join('\n');


    // Generate the JavaScript module
    const jsContent = `/**
 * System Prompt for AI Requirements Expansion
 * 
 * This file is auto-generated from system-prompt.md and template files during build.
 * Do not edit this file directly - edit the source markdown files instead.
 */

/**
 * The system prompt template that defines the AI assistant's role and behavior
 * @type {string}
 */
export const SYSTEM_PROMPT_TEMPLATE = ${JSON.stringify(cleanPrompt, null, 2)};

/**
 * The page relevancy analysis prompt template
 * @type {string}
 */
export const PAGE_RELEVANCY_PROMPT_TEMPLATE = ${JSON.stringify(cleanRelevancyPrompt, null, 2)};

${templateConstants}

/**
 * Array of all available templates
 * @type {Array<{name: string, constantName: string, content: string}>}
 */
export const TEMPLATES = [
${templateArray}
];

/**
 * Get default template for specific issue type
 * @param {string} issueType - Issue type (${templates.map(t => t.name).join(', ')})
 * @returns {string} Default template content
 */
export function getDefaultTemplate(issueType) {
  const normalizedType = issueType?.toLowerCase() || 'story';
  
  switch (normalizedType) {
${switchCases}
    default:
      return ''; // Empty fallback for unknown issue types
  }
}

/**
 * Create a complete system prompt by substituting template variables
 * @param {string} issueTitle - The Jira issue title/summary
 * @param {string} issueRequirements - The user's input requirements
 * @param {string} template - The template to use for the response structure
 * @param {string} projectContext - Optional project context from Confluence page
 * @returns {string} The complete system prompt with substituted values
 */
export function createFullPrompt(issueTitle, issueRequirements, template, projectContext = '') {
  const contextText = projectContext && projectContext.trim() 
    ? projectContext 
    : 'No additional project context available.';
    
  return SYSTEM_PROMPT_TEMPLATE
    .replace('{{ISSUE_REQUIREMENTS}}', issueRequirements)
    .replace('{{ISSUE_TITLE}}', issueTitle)
    .replace('{{RESPONSE_TEMPLATE}}', template)
    .replace('{{PROJECT_CONTEXT}}', contextText);
}

/**
 * Create a page relevancy analysis prompt by substituting template variables
 * @param {string} issueTitle - The Jira issue title/summary
 * @param {string} issuePrompt - The user's input prompt/requirements
 * @param {string} issueType - The issue type (Story, Task, Bug, etc.)
 * @param {Array} availablePages - Array of page objects with {title, pageId, spaceKey}
 * @returns {string} The complete page relevancy prompt with substituted values
 */
export function createPageRelevancyPrompt(issueTitle, issuePrompt, issueType, availablePages) {
  const pagesList = availablePages.map(page => 
    \`{\\nName: "\${page.title}",\\nID: \${page.pageId}\\n}\`
  ).join(',\\n');

  return PAGE_RELEVANCY_PROMPT_TEMPLATE
    .replace('{{ISSUE_TITLE}}', issueTitle)
    .replace('{{ISSUE_PROMPT}}', issuePrompt)
    .replace('{{ISSUE_TYPE}}', issueType)
    .replace('{{PAGES_LIST}}', \`{\\n\${pagesList}\\n}\`);
}
`;
    
    // Ensure build directory exists
    const buildDir = join(projectRoot, 'build');
    if (!existsSync(buildDir)) {
      mkdirSync(buildDir, { recursive: true });
    }
    
    // Write the generated module
    const outputPath = join(buildDir, 'system-prompt.js');
    writeFileSync(outputPath, jsContent);
    
    console.log(`✅ Successfully generated build/system-prompt.js from system-prompt.md, page-relevancy-prompt.md and ${templates.length} template files`);
    
  } catch (error) {
    console.error('❌ Failed to build prompts:', error);
    process.exit(1);
  }
}

buildPrompts();