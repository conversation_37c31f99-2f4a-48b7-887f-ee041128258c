
/**
 * Admin Resolver
 * 
 * Handles admin UI interactions for settings management.
 * Provides methods for loading, saving, and resetting admin settings.
 */

import { SettingsService } from '../services/SettingsService.js';
import { JiraService } from '../services/JiraService.js';
import { ConfluenceService } from '../services/ConfluenceService.js';

/**
 * Admin handler for settings UI
 */
export const adminHandler = async (req) => {
  const settingsService = new SettingsService();

  try {
    // Handle Forge bridge call format - method and payload are nested in req.call.payload
    const method = req.call.payload.method;
    const payload = req.call.payload.payload;
    
    switch (method) {
      case 'getSettings':
        return await handleGetSettings(settingsService, req);
      
      case 'saveSettings':
        return await handleSaveSettings(settingsService, payload, req);
      
      case 'resetToDefaults':
        return await handleResetToDefaults(settingsService, req);
      
      // Template methods
      case 'saveTemplateByType':
        return await handleSaveTemplateByType(settingsService, payload, req);
      
      case 'resetTemplatesToDefaults':
        return await handleResetTemplatesToDefaults(settingsService, req);
      
      case 'getProjectIssueTypes':
        return await handleGetProjectIssueTypes(payload);
      
      case 'getTemplatesForIssueTypes':
        return await handleGetTemplatesForIssueTypes(settingsService, payload, req);
      
      case 'resetTemplateToDefault':
        return await handleResetTemplateToDefault(settingsService, payload, req);
      
      case 'getCurrentProjectContext':
        return await handleGetCurrentProjectContext(req);
      
      case 'getConfluenceSpaces':
        return await handleGetConfluenceSpaces();
      
      case 'getConfluencePages':
        return await handleGetConfluencePages(payload);
      
      case 'setConfluencePageConfig':
        return await handleSetConfluencePageConfig(payload, req);
      
      case 'clearConfluencePageConfig':
        return await handleClearConfluencePageConfig(req);
      
      case 'getConfluencePageConfig':
        return await handleGetConfluencePageConfig(req);
      
      // New spaces and pages list methods
      case 'getConfluenceSpacesList':
        return await handleGetConfluenceSpacesList(req);
      
      case 'addConfluenceSpace':
        return await handleAddConfluenceSpace(payload, req);
      
      case 'removeConfluenceSpace':
        return await handleRemoveConfluenceSpace(payload, req);
      
      case 'getConfluencePagesList':
        return await handleGetConfluencePagesList(req);
      
      case 'addConfluencePage':
        return await handleAddConfluencePage(payload, req);
      
      case 'removeConfluencePage':
        return await handleRemoveConfluencePage(payload, req);
      
      case 'clearAllConfluenceConfiguration':
        return await handleClearAllConfluenceConfiguration(req);
      
      // Output field configuration methods
      case 'getFieldConfigurationForIssueTypes':
        return await handleGetFieldConfigurationForIssueTypes(settingsService, payload, req);
      
      case 'saveOutputFieldsForIssueTypes':
        return await handleSaveOutputFieldsForIssueTypes(settingsService, payload, req);
      
      default:
        return {
          success: false,
          error: `Unknown method: ${method}`
        };
    }
  } catch (error) {
    console.error('Error in admin handler:', error);
    return {
      success: false,
      error: error.message || 'Internal server error'
    };
  }
};

function getProjectIdFromRequest(req) {
  const projectId = req.context?.extension?.project?.id;
  if (!projectId) {
    throw new Error('Project ID not found in request context');
  }
  return projectId;
}

function createSuccessResponse(data) {
  return { success: true, ...data };
}

function createErrorResponse(error, defaultMessage) {
  return {
    success: false,
    error: defaultMessage || error.message || 'Unknown error'
  };
}

async function handleWithErrorHandling(operation, errorMessage) {
  try {
    return await operation();
  } catch (error) {
    console.error(`${errorMessage}:`, error);
    return createErrorResponse(error, errorMessage);
  }
}

async function handleGetSettings(settingsService, req) {
  return handleWithErrorHandling(async () => {
    const projectId = getProjectIdFromRequest(req);
    const settings = await settingsService.getSettings(projectId);
    return createSuccessResponse({ settings });
  }, 'Failed to load settings');
}

async function handleSaveSettings(settingsService, payload, req) {
  return handleWithErrorHandling(async () => {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.settings) {
      return createErrorResponse(new Error('Settings data is required'));
    }

    const validationError = validateSettings(payload.settings);
    if (validationError) {
      return createErrorResponse(new Error(validationError));
    }

    const success = await settingsService.saveSettings(payload.settings, projectId);
    
    if (success) {
      return createSuccessResponse({ message: 'Settings saved successfully' });
    } else {
      return createErrorResponse(new Error('Failed to save settings'));
    }
  }, 'Failed to save settings');
}

/**
 * Handle reset to defaults request
 */
async function handleResetToDefaults(settingsService, req) {
  return handleWithErrorHandling(async () => {
    const projectId = getProjectIdFromRequest(req);
    const settings = await settingsService.resetToDefaults(projectId);
    return createSuccessResponse({ settings, message: 'Settings reset to defaults' });
  }, 'Failed to reset settings');
}

function validateSettings(settings) {
  // Validate story template
  if (settings.storyTemplate !== undefined) {
    if (typeof settings.storyTemplate !== 'string') {
      return 'Story template must be a string';
    }
    if (settings.storyTemplate.trim().length === 0) {
      return 'Story template cannot be empty';
    }
  }

  // Validate AI model
  if (settings.aiModel !== undefined) {
    if (typeof settings.aiModel !== 'string') {
      return 'AI model must be a string';
    }
  }

  // Validate temperature
  if (settings.temperature !== undefined) {
    const temp = parseFloat(settings.temperature);
    if (isNaN(temp) || temp < 0 || temp > 2) {
      return 'Temperature must be a number between 0 and 2';
    }
  }

  // Validate max tokens
  if (settings.maxTokens !== undefined) {
    const tokens = parseInt(settings.maxTokens);
    if (isNaN(tokens) || tokens < 1 || tokens > 4000) {
      return 'Max tokens must be a number between 1 and 4000';
    }
  }


  return null; // No validation errors
}



/**
 * Handle save template by type request
 */
async function handleSaveTemplateByType(settingsService, payload, req) {
  try {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.issueType || !payload.template) {
      return {
        success: false,
        error: 'Issue type and template are required'
      };
    }

    // Validate template
    const validationError = validateTemplate(payload.template);
    if (validationError) {
      return {
        success: false,
        error: validationError
      };
    }

    const success = await settingsService.saveTemplateForIssueType(payload.issueType, payload.template, projectId);
    
    if (success) {
      return {
        success: true,
        message: `Template saved successfully for ${payload.issueType}`
      };
    } else {
      return {
        success: false,
        error: 'Failed to save template'
      };
    }
  } catch (error) {
    console.error(`Error saving template for issue type ${payload?.issueType}:`, error);
    return {
      success: false,
      error: 'Failed to save template'
    };
  }
}

/**
 * Handle reset templates to defaults request
 */
async function handleResetTemplatesToDefaults(settingsService, req) {
  try {
    const projectId = getProjectIdFromRequest(req);
    const defaultTemplates = await settingsService.resetTemplatesToDefaults(projectId);
    return {
      success: true,
      templates: defaultTemplates,
      message: 'All templates reset to defaults'
    };
  } catch (error) {
    console.error('Error resetting templates to defaults:', error);
    return {
      success: false,
      error: `Failed to reset templates: ${error.message || 'Unknown error'}`
    };
  }
}

/**
 * Validate template content
 */
function validateTemplate(template) {
  if (typeof template !== 'string') {
    return 'Template must be a string';
  }
  if (template.trim().length === 0) {
    return 'Template cannot be empty';
  }
  if (template.length > 10000) {
    return 'Template is too long (maximum 10,000 characters)';
  }
  return null; // No validation errors
}


/**
 * Handle get project issue types request
 */
async function handleGetProjectIssueTypes(payload) {
  try {
    if (!payload || !payload.projectId) {
      return {
        success: false,
        error: 'Project ID is required'
      };
    }

    const jiraService = new JiraService();
    const issueTypes = await jiraService.getProjectIssueTypes(payload.projectId);
    
    return {
      success: true,
      issueTypes
    };
  } catch (error) {
    console.error(`Error getting issue types for project ${payload?.projectId}:`, error);
    return {
      success: false,
      error: 'Failed to load project issue types'
    };
  }
}

/**
 * Handle get templates for multiple issue types request
 */
async function handleGetTemplatesForIssueTypes(settingsService, payload, req) {
  try {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.issueTypes || !Array.isArray(payload.issueTypes)) {
      return {
        success: false,
        error: 'Issue types array is required'
      };
    }

    const templates = await settingsService.getTemplatesForIssueTypes(payload.issueTypes, projectId);
    
    return {
      success: true,
      templates
    };
  } catch (error) {
    console.error(`Error getting templates for issue types:`, error);
    return {
      success: false,
      error: 'Failed to load templates for issue types'
    };
  }
}

/**
 * Handle reset single template to default request
 */
async function handleResetTemplateToDefault(settingsService, payload, req) {
  try {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.issueType) {
      return {
        success: false,
        error: 'Issue type is required'
      };
    }

    // Delete the stored template for this specific issue type
    await settingsService.deleteTemplateForIssueType(payload.issueType, projectId);
    
    // Get the default template for this issue type
    const defaultTemplate = settingsService.getDefaultTemplateForIssueType(payload.issueType);
    
    return {
      success: true,
      template: defaultTemplate,
      issueType: payload.issueType,
      message: `Template reset to default for ${payload.issueType}`
    };
  } catch (error) {
    console.error(`Error resetting template for issue type ${payload?.issueType}:`, error);
    return {
      success: false,
      error: 'Failed to reset template to default'
    };
  }
}

/**
 * Handle get current project context request
 */
async function handleGetCurrentProjectContext(req) {
  try {
    // Project pages have access to project context via extension data
    const context = req.context;
    
    let projectId = null;
    
    // For jira:projectPage, the project ID is available in extension.project.id
    if (context?.extension?.project?.id) {
      projectId = context.extension.project.id;
    }
    
    if (!projectId) {
      console.error('No project ID found in project page context');
      return {
        success: false,
        error: 'Could not determine project context from project page'
      };
    }
    
    
    return {
      success: true,
      projectId: projectId
    };
  } catch (error) {
    console.error('Error getting project context:', error.message);
    return {
      success: false,
      error: 'Failed to get project context'
    };
  }
}

/**
 * Get all available Confluence spaces
 */
async function handleGetConfluenceSpaces() {
  try {
    const confluenceService = new ConfluenceService();
    const spaces = await confluenceService.getSpaces();
    
    return {
      success: true,
      spaces: spaces
    };
  } catch (error) {
    console.error('Error getting Confluence spaces:', error);
    return {
      success: false,
      error: 'Failed to get Confluence spaces',
      spaces: []
    };
  }
}

/**
 * Get ALL pages in a specific Confluence space (with full pagination)
 */
async function handleGetConfluencePages(payload) {
  try {
    const { spaceKey } = payload;
    
    if (!spaceKey) {
      return {
        success: false,
        error: 'Space key is required',
        pages: []
      };
    }
    
    const { getAllPagesFromSpace } = await import('../utils/confluence-helpers.js');
    const pages = await getAllPagesFromSpace(spaceKey);
    
    return {
      success: true,
      pages: pages
    };
  } catch (error) {
    console.error('Error getting all Confluence pages:', error);
    return {
      success: false,
      error: 'Failed to get Confluence pages',
      pages: []
    };
  }
}

/**
 * Set the configured Confluence page for project context
 */
async function handleSetConfluencePageConfig(payload, req) {
  try {
    const { pageId, spaceKey } = payload;
    
    
    // Validate pageId
    if (!pageId || typeof pageId !== 'string' || pageId.trim() === '') {
      return {
        success: false,
        error: 'Page ID is required and must be a non-empty string'
      };
    }
    
    // Validate spaceKey
    if (!spaceKey || typeof spaceKey !== 'string' || spaceKey.trim() === '') {
      return {
        success: false,
        error: 'Space key is required and must be a non-empty string'
      };
    }
    
    // Get project context
    const projectContextResult = await handleGetCurrentProjectContext(req);
    if (!projectContextResult.success) {
      return projectContextResult;
    }
    
    const projectId = projectContextResult.projectId;
    
    const confluenceService = new ConfluenceService();
    
    const success = await confluenceService.setConfiguredPageConfig(projectId, pageId.trim(), spaceKey.trim());
    
    if (success) {
      return {
        success: true,
        message: 'Confluence page configuration saved successfully'
      };
    } else {
      return {
        success: false,
        error: 'Failed to save Confluence page configuration'
      };
    }
  } catch (error) {
    console.error('Error setting Confluence page config:', error);
    return {
      success: false,
      error: 'Failed to save Confluence page configuration'
    };
  }
}

/**
 * Clear the configured Confluence page for project context
 */
async function handleClearConfluencePageConfig(req) {
  try {
    // Get project context
    const projectContextResult = await handleGetCurrentProjectContext(req);
    if (!projectContextResult.success) {
      return projectContextResult;
    }
    
    const projectId = projectContextResult.projectId;
    const confluenceService = new ConfluenceService();
    
    const success = await confluenceService.clearConfiguredPageConfig(projectId);
    
    if (success) {
      return {
        success: true,
        message: 'Confluence page configuration cleared successfully'
      };
    } else {
      return {
        success: false,
        error: 'Failed to clear Confluence page configuration'
      };
    }
  } catch (error) {
    console.error('Error clearing Confluence page config:', error);
    return {
      success: false,
      error: error.message || 'Internal server error'
    };
  }
}

/**
 * Get the configured Confluence page for project context
 */
async function handleGetConfluencePageConfig(req) {
  try {
    // Get project context
    const projectContextResult = await handleGetCurrentProjectContext(req);
    if (!projectContextResult.success) {
      return projectContextResult;
    }
    
    const projectId = projectContextResult.projectId;
    const confluenceService = new ConfluenceService();
    
    const config = await confluenceService.getConfiguredPageConfig(projectId);
    
    const result = {
      success: true,
      pageId: config?.pageId || null,
      spaceKey: config?.spaceKey || null
    };
    
    return result;
  } catch (error) {
    console.error('Error getting Confluence page config:', error);
    return {
      success: false,
      error: 'Failed to get Confluence page configuration',
      pageId: null
    };
  }
}



/**
 * Handle get complete field configuration for multiple issue types request (individual API calls fallback)
 * Returns both available paragraph fields and current output field selection for each issue type
 */
async function handleGetFieldConfigurationForIssueTypesIndividual(settingsService, payload, projectId) {
  const jiraService = new JiraService();
  const issueTypeFieldData = {};

  // Process all issue types in parallel for better performance
  const issueTypePromises = payload.issueTypes.map(async (issueType) => {
    try {
      // Get both paragraph fields and output field configuration in parallel
      const [paragraphFields, outputFields] = await Promise.all([
        jiraService.getParagraphFieldsForProjectAndIssueType(projectId, issueType.id),
        settingsService.getOutputFieldsForIssueTypes([issueType.name], projectId)
      ]);

      const selectedOutputField = outputFields[issueType.name] || '';

      return {
        issueTypeName: issueType.name,
        data: {
          paragraphFields,
          selectedOutputField,
          issueTypeId: issueType.id
        }
      };
    } catch (error) {
      console.error(`Error processing issue type ${issueType.name}:`, error);
      // Return error data instead of throwing
      return {
        issueTypeName: issueType.name,
        data: {
          paragraphFields: [],
          selectedOutputField: '',
          issueTypeId: issueType.id,
          error: `Failed to load data for ${issueType.name}`
        }
      };
    }
  });

  // Wait for all issue types to be processed
  const results = await Promise.all(issueTypePromises);

  // Build the final data structure
  results.forEach(result => {
    issueTypeFieldData[result.issueTypeName] = result.data;
  });

  return {
    success: true,
    issueTypeFieldData
  };
}

/**
 * Handle get complete field configuration for multiple issue types request
 * Returns both available paragraph fields and current output field selection for each issue type
 */
async function handleGetFieldConfigurationForIssueTypes(settingsService, payload, req) {
  try {
    const projectId = getProjectIdFromRequest(req);

    if (!payload || !payload.issueTypes || !Array.isArray(payload.issueTypes)) {
      return {
        success: false,
        error: 'Issue types array is required'
      };
    }

    const jiraService = new JiraService();
    const issueTypeFieldData = {};

    try {
      // Get project information to obtain the project key
      const project = await jiraService.getProject(projectId);

      if (!project || !project.key) {
        console.warn(`Could not get project key for project ${projectId}, falling back to individual API calls`);
        // Fallback to individual API calls if we can't get the project key
        return await handleGetFieldConfigurationForIssueTypesIndividual(settingsService, payload, projectId);
      }

      // Use the optimized bulk API call to get all paragraph fields at once
      const [bulkParagraphFields, allOutputFields] = await Promise.all([
        jiraService.getParagraphFieldsForAllIssueTypes(projectId, project.key, payload.issueTypes),
        settingsService.getOutputFieldsForIssueTypes(payload.issueTypes.map(it => it.name), projectId)
      ]);

      // Build the final data structure
      for (const issueType of payload.issueTypes) {
        const paragraphFields = bulkParagraphFields[issueType.id] || [];
        const selectedOutputField = allOutputFields[issueType.name] || '';

        issueTypeFieldData[issueType.name] = {
          paragraphFields,
          selectedOutputField,
          issueTypeId: issueType.id
        };
      }
    } catch (error) {
      console.error('Error in bulk field configuration loading, falling back to individual calls:', error);
      // Fallback to individual API calls if bulk approach fails
      return await handleGetFieldConfigurationForIssueTypesIndividual(settingsService, payload, projectId);
    }

    return {
      success: true,
      issueTypeFieldData
    };
  } catch (error) {
    console.error(`Error getting field configuration for issue types:`, error);
    return {
      success: false,
      error: 'Failed to load field configuration for issue types'
    };
  }
}

/**
 * Handle save output field configurations for multiple issue types request
 */
async function handleSaveOutputFieldsForIssueTypes(settingsService, payload, req) {
  try {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.outputFields || typeof payload.outputFields !== 'object') {
      return {
        success: false,
        error: 'Output fields configuration is required'
      };
    }

    const savePromises = [];
    for (const [issueType, fieldName] of Object.entries(payload.outputFields)) {
      if (fieldName && fieldName.trim() !== '') {
        savePromises.push(settingsService.saveOutputFieldForIssueType(issueType, fieldName, projectId));
      } else {
        // If field name is empty, delete the configuration to use default
        savePromises.push(settingsService.deleteOutputFieldForIssueType(issueType, projectId));
      }
    }

    const results = await Promise.all(savePromises);
    const allSuccessful = results.every(result => result === true);
    
    if (allSuccessful) {
      return {
        success: true,
        message: 'Output field configurations saved successfully'
      };
    } else {
      return {
        success: false,
        error: 'Failed to save some output field configurations'
      };
    }
  } catch (error) {
    console.error(`Error saving output fields:`, error);
    return {
      success: false,
      error: 'Failed to save output field configurations'
    };
  }
}

/**
 * Get configured Confluence spaces list for a project
 */
async function handleGetConfluenceSpacesList(req) {
  try {
    const projectContextResult = await handleGetCurrentProjectContext(req);
    if (!projectContextResult.success) {
      return projectContextResult;
    }
    
    const projectId = projectContextResult.projectId;
    const confluenceService = new ConfluenceService();
    
    const spaces = await confluenceService.getConfiguredSpaces(projectId);
    
    return {
      success: true,
      spaces
    };
  } catch (error) {
    console.error('Error getting Confluence spaces list:', error);
    return {
      success: false,
      error: 'Failed to get Confluence spaces list',
      spaces: []
    };
  }
}

/**
 * Add a space to the configured Confluence spaces list
 */
async function handleAddConfluenceSpace(payload, req) {
  try {
    const { spaceKey, spaceName } = payload;
    
    if (!spaceKey || typeof spaceKey !== 'string' || spaceKey.trim() === '') {
      return {
        success: false,
        error: 'Space key is required and must be a non-empty string'
      };
    }
    
    if (!spaceName || typeof spaceName !== 'string' || spaceName.trim() === '') {
      return {
        success: false,
        error: 'Space name is required and must be a non-empty string'
      };
    }
    
    const projectContextResult = await handleGetCurrentProjectContext(req);
    if (!projectContextResult.success) {
      return projectContextResult;
    }
    
    const projectId = projectContextResult.projectId;
    const confluenceService = new ConfluenceService();
    
    const success = await confluenceService.addConfiguredSpace(projectId, spaceKey.trim(), spaceName.trim());
    
    if (success) {
      return {
        success: true,
        message: 'Space added to configuration successfully'
      };
    } else {
      return {
        success: false,
        error: 'Failed to add space to configuration'
      };
    }
  } catch (error) {
    console.error('Error adding Confluence space:', error);
    return {
      success: false,
      error: 'Failed to add space to configuration'
    };
  }
}

/**
 * Remove a space from the configured Confluence spaces list
 */
async function handleRemoveConfluenceSpace(payload, req) {
  try {
    const { spaceKey } = payload;
    
    if (!spaceKey || typeof spaceKey !== 'string' || spaceKey.trim() === '') {
      return {
        success: false,
        error: 'Space key is required and must be a non-empty string'
      };
    }
    
    const projectContextResult = await handleGetCurrentProjectContext(req);
    if (!projectContextResult.success) {
      return projectContextResult;
    }
    
    const projectId = projectContextResult.projectId;
    const confluenceService = new ConfluenceService();
    
    const success = await confluenceService.removeConfiguredSpace(projectId, spaceKey.trim());
    
    if (success) {
      return {
        success: true,
        message: 'Space removed from configuration successfully'
      };
    } else {
      return {
        success: false,
        error: 'Failed to remove space from configuration'
      };
    }
  } catch (error) {
    console.error('Error removing Confluence space:', error);
    return {
      success: false,
      error: 'Failed to remove space from configuration'
    };
  }
}

/**
 * Get configured Confluence pages list for a project
 */
async function handleGetConfluencePagesList(req) {
  try {
    const projectContextResult = await handleGetCurrentProjectContext(req);
    if (!projectContextResult.success) {
      return projectContextResult;
    }
    
    const projectId = projectContextResult.projectId;
    const confluenceService = new ConfluenceService();
    
    const pages = await confluenceService.getConfiguredPages(projectId);
    
    return {
      success: true,
      pages
    };
  } catch (error) {
    console.error('Error getting Confluence pages list:', error);
    return {
      success: false,
      error: 'Failed to get Confluence pages list',
      pages: []
    };
  }
}

/**
 * Add a page to the configured Confluence pages list
 */
async function handleAddConfluencePage(payload, req) {
  try {
    const { pageId, pageTitle, spaceKey } = payload;
    
    if (!pageId || typeof pageId !== 'string' || pageId.trim() === '') {
      return {
        success: false,
        error: 'Page ID is required and must be a non-empty string'
      };
    }
    
    if (!pageTitle || typeof pageTitle !== 'string' || pageTitle.trim() === '') {
      return {
        success: false,
        error: 'Page title is required and must be a non-empty string'
      };
    }
    
    if (!spaceKey || typeof spaceKey !== 'string' || spaceKey.trim() === '') {
      return {
        success: false,
        error: 'Space key is required and must be a non-empty string'
      };
    }
    
    const projectContextResult = await handleGetCurrentProjectContext(req);
    if (!projectContextResult.success) {
      return projectContextResult;
    }
    
    const projectId = projectContextResult.projectId;
    const confluenceService = new ConfluenceService();
    
    const success = await confluenceService.addConfiguredPage(projectId, pageId.trim(), pageTitle.trim(), spaceKey.trim());
    
    if (success) {
      return {
        success: true,
        message: 'Page added to configuration successfully'
      };
    } else {
      return {
        success: false,
        error: 'Failed to add page to configuration'
      };
    }
  } catch (error) {
    console.error('Error adding Confluence page:', error);
    return {
      success: false,
      error: 'Failed to add page to configuration'
    };
  }
}

/**
 * Remove a page from the configured Confluence pages list
 */
async function handleRemoveConfluencePage(payload, req) {
  try {
    const { pageId } = payload;
    
    if (!pageId || typeof pageId !== 'string' || pageId.trim() === '') {
      return {
        success: false,
        error: 'Page ID is required and must be a non-empty string'
      };
    }
    
    const projectContextResult = await handleGetCurrentProjectContext(req);
    if (!projectContextResult.success) {
      return projectContextResult;
    }
    
    const projectId = projectContextResult.projectId;
    const confluenceService = new ConfluenceService();
    
    const success = await confluenceService.removeConfiguredPage(projectId, pageId.trim());
    
    if (success) {
      return {
        success: true,
        message: 'Page removed from configuration successfully'
      };
    } else {
      return {
        success: false,
        error: 'Failed to remove page from configuration'
      };
    }
  } catch (error) {
    console.error('Error removing Confluence page:', error);
    return {
      success: false,
      error: 'Failed to remove page from configuration'
    };
  }
}

/**
 * Clear all Confluence configuration (both spaces and pages lists)
 */
async function handleClearAllConfluenceConfiguration(req) {
  try {
    const projectContextResult = await handleGetCurrentProjectContext(req);
    if (!projectContextResult.success) {
      return projectContextResult;
    }
    
    const projectId = projectContextResult.projectId;
    const confluenceService = new ConfluenceService();
    
    const success = await confluenceService.clearAllConfiguration(projectId);
    
    if (success) {
      return {
        success: true,
        message: 'All Confluence configuration cleared successfully'
      };
    } else {
      return {
        success: false,
        error: 'Failed to clear all Confluence configuration'
      };
    }
  } catch (error) {
    console.error('Error clearing all Confluence configuration:', error);
    return {
      success: false,
      error: 'Failed to clear all Confluence configuration'
    };
  }
}