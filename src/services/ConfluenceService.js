/**
 * Confluence Service - Handles Confluence API operations and content caching
 */

import { kvs } from '@forge/kvs';
import { convert as adfToMd } from 'adf-to-md';
import {
  getSpaces,
  getPagesByIds,
  getPageContent as getPageContentHelper,
  searchPagesByTitle as searchPagesByTitleHelper,
  getAllPagesFromSpaces
} from '../utils/confluence-helpers.js';

export class ConfluenceService {
  constructor() {
    // Default cache TTL in milliseconds (5 minutes)
    this.defaultCacheTTL = 5 * 60 * 1000;
  }

  /**
   * Check if cached content is still valid
   * @param {Object} cachedContent - Cached content with timestamp
   * @returns {boolean} True if cache is valid
   */
  isCacheValid(cachedContent) {
    if (!cachedContent || !cachedContent.timestamp) {
      return false;
    }
    
    const now = Date.now();
    const age = now - cachedContent.timestamp;
    
    return age < this.defaultCacheTTL;
  }

  /**
   * Get multiple pages by IDs with intelligent caching (batch API + cache)
   * @param {Array} pageIds - Array of page IDs to fetch
   * @param {string} projectId - Project ID for cache key
   * @returns {Promise<Array>} Array of page objects with content
   */
  async getPagesByIdsWithCache(pageIds, projectId) {
    try {
      if (!pageIds || pageIds.length === 0) {
        return [];
      }

      // Check cache for each page
      const cachedPages = [];
      const uncachedPageIds = [];
      
      for (const pageId of pageIds) {
        const cacheKey = `confluence-cache-project-${projectId}-page-${pageId}`;
        const cachedData = await kvs.get(cacheKey);
        
        if (cachedData && this.isCacheValid(cachedData)) {
          cachedPages.push(cachedData.data);
        } else {
          uncachedPageIds.push(pageId);
        }
      }
      
      console.log(`Cache check: Found ${cachedPages.length} pages in cache, fetching ${uncachedPageIds.length} from API`);
      
      // Fetch uncached pages using batch CQL request
      let fetchedPages = [];
      if (uncachedPageIds.length > 0) {
        fetchedPages = await getPagesByIds(uncachedPageIds, true); // includeContent = true
        
        // Cache the newly fetched pages
        for (const pageData of fetchedPages) {
          if (pageData && pageData.id) {
            const cacheKey = `confluence-cache-project-${projectId}-page-${pageData.id}`;
            const cacheData = {
              data: pageData,
              timestamp: Date.now(),
              version: pageData.version?.number || 1
            };
            await kvs.set(cacheKey, cacheData);
          }
        }
      }
      
      // Combine cached and fetched pages
      const allPages = [...cachedPages, ...fetchedPages];
      console.log(`Retrieved ${allPages.length} total pages (${cachedPages.length} from cache + ${fetchedPages.length} from API)`);
      
      return allPages;
    } catch (error) {
      console.error('Error getting pages by IDs with cache:', error);
      return [];
    }
  }

  /**
   * Get all available Confluence spaces
   * @returns {Promise<Array>} Array of space objects
   */
  async getSpaces() {
    return await getSpaces();
  }

  /**
   * Get ALL pages in a specific space with full pagination (titles only, no content)
   * @param {string} spaceKey - Space key to search in
   * @returns {Promise<Array>} Array of all page objects (titles and metadata only)
   */
  async getPagesInSpace(spaceKey) {
    const { getAllPagesFromSpace } = await import('../utils/confluence-helpers.js');
    return await getAllPagesFromSpace(spaceKey);
  }


  /**
   * Get a specific page by ID with content
   * @param {string} pageId - Page ID to fetch
   * @param {string} projectId - Project ID for cache key (optional)
   * @param {boolean} useCache - Whether to use caching (optional)
   * @returns {Promise<Object|null>} Page object with content or null if not found
   */
  async getPageContent(pageId, projectId, useCache = false) {
    try {
      // Check cache first if requested
      if (useCache && projectId) {
        const cacheKey = `confluence-cache-project-${projectId}-page-${pageId}`;
        const cachedData = await kvs.get(cacheKey);
        
        if (cachedData && this.isCacheValid(cachedData)) {
          return cachedData.data;
        }
      }

      // Fetch from Confluence API using helper
      const pageData = await getPageContentHelper(pageId);

      if (!pageData) {
        return null;
      }

      // Cache the result if requested
      if (useCache && projectId && pageData) {
        const cacheKey = `confluence-cache-project-${projectId}-page-${pageId}`;
        const cacheData = {
          data: pageData,
          timestamp: Date.now(),
          version: pageData.version?.number || 1
        };
        await kvs.set(cacheKey, cacheData);
      }

      return pageData;
    } catch (error) {
      console.error(`Error fetching page content for ${pageId}:`, error);
      return null;
    }
  }

  /**
   * Extract plain text content from Confluence page
   * @param {Object} pageData - Page data from Confluence API
   * @returns {string} Plain text content
   */
  extractPlainTextContent(pageData) {
    try {
      if (!pageData || !pageData.body) {
        return '';
      }

      // Handle both ADF and view formats
      let content = '';
      
      if (pageData.body.atlas_doc_format) {
        // Get the ADF content
        let adfContent = pageData.body.atlas_doc_format.value;
        
        // Parse ADF content if it's a string
        if (typeof adfContent === 'string') {
          try {
            adfContent = JSON.parse(adfContent);
          } catch (parseError) {
            console.error('Error parsing ADF JSON:', parseError);
            return '';
          }
        }
        
        // Convert ADF to Markdown
        const markdown = adfToMd(adfContent).result || '';
        return markdown;
      } else if (pageData.body.view && pageData.body.view.value) {
        // Handle HTML view format for testing
        const htmlContent = pageData.body.view.value;
        
        // Simple HTML to text conversion
        content = htmlContent
          .replace(/<h[1-6][^>]*>/gi, '')
          .replace(/<\/h[1-6]>/gi, '\n\n')
          .replace(/<p[^>]*>/gi, '')
          .replace(/<\/p>/gi, '\n\n')
          .replace(/<br\s*\/?>/gi, '\n')
          .replace(/<div[^>]*>/gi, '')
          .replace(/<\/div>/gi, '')
          .replace(/<[^>]*>/g, '')
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&quot;/g, '"')
          .replace(/&nbsp;/g, '')
          .trim();
        
        return content;
      }
      
      return '';
    } catch (error) {
      console.error('Error converting page content:', error);
      return '';
    }
  }


  /**
   * Get project context using specific relevant page IDs (for use after separate AI relevancy analysis)
   * @param {string} projectId - Project ID
   * @param {Array} relevantPageIds - Array of page IDs deemed relevant by AI
   * @returns {Promise<string>} Project context text or empty string
   */
  async getProjectContextWithRelevantPages(projectId, relevantPageIds) {
    try {
      // Always include mandatory configured pages
      const configuredPages = await this.getConfiguredPages(projectId);
      
      // Get unique page IDs (mandatory pages + AI-recommended pages, deduplicated)
      const mandatoryPageIds = configuredPages.map(p => p.pageId);
      const aiRecommendedPageIds = relevantPageIds.filter(id => !mandatoryPageIds.includes(id));
      const allPageIds = [...mandatoryPageIds, ...aiRecommendedPageIds];
      
      if (allPageIds.length === 0) {
        console.log(`No relevant Confluence pages for project ${projectId}`);
        return '';
      }

      // Fetch content from all relevant pages using optimized batch method with caching
      const allPages = await this.getPagesByIdsWithCache(allPageIds, projectId);
      
      // Process content for all pages
      const pageContents = allPages.map((pageData) => {
        try {
          if (!pageData) {
            return '';
          }
          const content = this.extractPlainTextContent(pageData);
          const pageTitle = pageData.title || `Page ${pageData.id}`;
          return content ? `## ${pageTitle}\n\n${content}` : '';
        } catch (error) {
          console.error(`Error processing page ${pageData.id}:`, error);
          return '';
        }
      });

      // Combine all page contents
      const allContext = pageContents.filter(content => content).join('\n\n---\n\n');
      
      console.log(`Retrieved ${allContext.length} characters of context from ${allPageIds.length} pages (${mandatoryPageIds.length} mandatory + ${aiRecommendedPageIds.length} AI-recommended)`);
      return allContext;
    } catch (error) {
      console.error(`Error getting project context with relevant pages for ${projectId}:`, error);
      return '';
    }
  }





  /**
   * Search for pages by title across all spaces
   * @param {string} title - Page title to search for
   * @param {number} limit - Maximum results to return
   * @returns {Promise<Array>} Array of matching pages
   */
  async searchPagesByTitle(title, limit = 10) {
    return await searchPagesByTitleHelper(title, limit);
  }

  /**
   * Get the configured Confluence spaces list for a project
   * @param {string} projectId - Project ID
   * @returns {Promise<Array>} Array of space configs [{key, name}] or empty array
   */
  async getConfiguredSpaces(projectId) {
    try {
      const storageKey = `confluence-spaces-project-${projectId}`;
      const spaces = await kvs.get(storageKey);
      return spaces || [];
    } catch (error) {
      console.error(`Error getting configured spaces for project ${projectId}:`, error);
      return [];
    }
  }

  /**
   * Add a space to the configured spaces list for a project
   * @param {string} projectId - Project ID
   * @param {string} spaceKey - Space key to add
   * @param {string} spaceName - Space name to add
   * @returns {Promise<boolean>} Success status
   */
  async addConfiguredSpace(projectId, spaceKey, spaceName) {
    try {
      if (!projectId || typeof projectId !== 'string') {
        throw new Error('Project ID must be a non-empty string');
      }
      
      if (!spaceKey || typeof spaceKey !== 'string') {
        throw new Error('Space key must be a non-empty string');
      }

      if (!spaceName || typeof spaceName !== 'string') {
        throw new Error('Space name must be a non-empty string');
      }

      const spaces = await this.getConfiguredSpaces(projectId);
      
      // Check if space already exists
      const existingSpace = spaces.find(space => space.key === spaceKey);
      if (existingSpace) {
        return true; // Already exists, consider it success
      }

      // Add the new space
      spaces.push({ key: spaceKey, name: spaceName });

      const storageKey = `confluence-spaces-project-${projectId}`;
      await kvs.set(storageKey, spaces);
      
      return true;
    } catch (error) {
      console.error(`Error adding configured space for project ${projectId}:`, error);
      return false;
    }
  }

  /**
   * Remove a space from the configured spaces list for a project
   * @param {string} projectId - Project ID
   * @param {string} spaceKey - Space key to remove
   * @returns {Promise<boolean>} Success status
   */
  async removeConfiguredSpace(projectId, spaceKey) {
    try {
      if (!projectId || typeof projectId !== 'string') {
        throw new Error('Project ID must be a non-empty string');
      }
      
      if (!spaceKey || typeof spaceKey !== 'string') {
        throw new Error('Space key must be a non-empty string');
      }

      const spaces = await this.getConfiguredSpaces(projectId);
      const filteredSpaces = spaces.filter(space => space.key !== spaceKey);

      const storageKey = `confluence-spaces-project-${projectId}`;
      await kvs.set(storageKey, filteredSpaces);
      
      return true;
    } catch (error) {
      console.error(`Error removing configured space for project ${projectId}:`, error);
      return false;
    }
  }

  /**
   * Get the configured Confluence pages list for a project
   * @param {string} projectId - Project ID
   * @returns {Promise<Array>} Array of page configs [{pageId, title, spaceKey}] or empty array
   */
  async getConfiguredPages(projectId) {
    try {
      const storageKey = `confluence-pages-project-${projectId}`;
      const pages = await kvs.get(storageKey);
      return pages || [];
    } catch (error) {
      console.error(`Error getting configured pages for project ${projectId}:`, error);
      return [];
    }
  }

  /**
   * Add a page to the configured pages list for a project
   * @param {string} projectId - Project ID
   * @param {string} pageId - Page ID to add
   * @param {string} pageTitle - Page title to add
   * @param {string} spaceKey - Space key the page belongs to
   * @returns {Promise<boolean>} Success status
   */
  async addConfiguredPage(projectId, pageId, pageTitle, spaceKey) {
    try {
      if (!projectId || typeof projectId !== 'string') {
        throw new Error('Project ID must be a non-empty string');
      }
      
      if (!pageId || typeof pageId !== 'string') {
        throw new Error('Page ID must be a non-empty string');
      }

      if (!pageTitle || typeof pageTitle !== 'string') {
        throw new Error('Page title must be a non-empty string');
      }

      if (!spaceKey || typeof spaceKey !== 'string') {
        throw new Error('Space key must be a non-empty string');
      }

      const pages = await this.getConfiguredPages(projectId);
      
      // Check if page already exists
      const existingPage = pages.find(page => page.pageId === pageId);
      if (existingPage) {
        return true; // Already exists, consider it success
      }

      // Add the new page
      pages.push({ pageId, title: pageTitle, spaceKey });

      const storageKey = `confluence-pages-project-${projectId}`;
      await kvs.set(storageKey, pages);
      
      return true;
    } catch (error) {
      console.error(`Error adding configured page for project ${projectId}:`, error);
      return false;
    }
  }

  /**
   * Remove a page from the configured pages list for a project
   * @param {string} projectId - Project ID
   * @param {string} pageId - Page ID to remove
   * @returns {Promise<boolean>} Success status
   */
  async removeConfiguredPage(projectId, pageId) {
    try {
      if (!projectId || typeof projectId !== 'string') {
        throw new Error('Project ID must be a non-empty string');
      }
      
      if (!pageId || typeof pageId !== 'string') {
        throw new Error('Page ID must be a non-empty string');
      }

      const pages = await this.getConfiguredPages(projectId);
      const filteredPages = pages.filter(page => page.pageId !== pageId);

      const storageKey = `confluence-pages-project-${projectId}`;
      await kvs.set(storageKey, filteredPages);
      
      return true;
    } catch (error) {
      console.error(`Error removing configured page for project ${projectId}:`, error);
      return false;
    }
  }

  /**
   * Get ALL pages from configured spaces for a project with full pagination (titles only, no content)
   * @param {string} projectId - Project ID
   * @returns {Promise<Array>} Array of all page objects (titles and metadata only)
   */
  async getAllPagesFromConfiguredSpaces(projectId) {
    try {
      const configuredSpaces = await this.getConfiguredSpaces(projectId);
      
      if (!configuredSpaces || configuredSpaces.length === 0) {
        console.log(`No configured spaces found for project ${projectId}`);
        return [];
      }
      
      const spaceKeys = configuredSpaces.map(space => space.key);
      const availablePages = await getAllPagesFromSpaces(spaceKeys);
      
      console.log(`Retrieved ${availablePages.length} pages from ${spaceKeys.length} configured spaces for project ${projectId} (with full pagination)`);
      return availablePages;
    } catch (error) {
      console.error(`Error getting all pages from configured spaces for project ${projectId}:`, error);
      return [];
    }
  }

  /**
   * Clear all Confluence configuration (both spaces and pages lists) for a project
   * @param {string} projectId - Project ID
   * @returns {Promise<boolean>} Success status
   */
  async clearAllConfiguration(projectId) {
    try {
      if (!projectId || typeof projectId !== 'string') {
        throw new Error('Project ID must be a non-empty string');
      }

      // Clear both spaces and pages lists by setting them to empty arrays
      const spacesStorageKey = `confluence-spaces-project-${projectId}`;
      const pagesStorageKey = `confluence-pages-project-${projectId}`;
      
      const [spacesSuccess, pagesSuccess] = await Promise.all([
        kvs.set(spacesStorageKey, []).then(() => true).catch(() => false),
        kvs.set(pagesStorageKey, []).then(() => true).catch(() => false)
      ]);
      
      return spacesSuccess && pagesSuccess;
    } catch (error) {
      console.error(`Error clearing all Confluence configuration for project ${projectId}:`, error);
      return false;
    }
  }
}