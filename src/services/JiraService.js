/**
 * Jira Service - Handles field resolution and issue operations
 */

import { getAllFieldsForProjectAndIssueType, updateIssueFields, isFieldUpdated, getIssue, getProjectIssueTypes, getParagraphFieldsForProjectAndIssueType, getParagraphFieldsForAllIssueTypes, getProject } from '../utils/jira-helpers.js';
import { SettingsService } from './SettingsService.js';
import { config } from '../config/index.js';

export class JiraService {
  
  /**
   * Get field information by field name(s)
   * @param {string|string[]} fieldNames - Single field name or array of field names
   * @param {string} projectId - Project ID for context-specific field resolution
   * @param {string} issueTypeId - Issue type ID for context-specific field resolution
   * @returns {Promise<Object|Object[]|null>} Field object(s) or null if not found
   */
  async getFieldsByName(fieldNames, projectId, issueTypeId) {
    const isArray = Array.isArray(fieldNames);
    const namesToProcess = isArray ? fieldNames : [fieldNames];
    
    // Get all fields for the project and issue type
    const allFields = await getAllFieldsForProjectAndIssueType(projectId, issueTypeId);
    
    if (allFields.length === 0) {
      return isArray ? [] : null;
    }
    
    const results = [];
    
    for (const fieldName of namesToProcess) {
      const foundField = allFields.find(f => f.name === fieldName);
      if (!foundField) {
        console.error(`Could not find field: ${fieldName}`);
        results.push(null);
        continue;
      }

      results.push(foundField);
    }
    
    return isArray ? results : results[0];
  }

  /**
   * Get issue details including all custom fields
   * @param {string} issueIdOrKey - The issue ID or key
   * @returns {Promise<Object|null>} Issue object or null if not found
   */
  async getIssue(issueIdOrKey) {
    return await getIssue(issueIdOrKey);
  }

  /**
   * Update a Jira issue with the given fields
   * @param {string} issueId - The issue ID
   * @param {Object} fields - Object containing field IDs as keys and values to update
   * @returns {Promise<boolean>} True if successful, false otherwise
   */
  async updateIssueFields(issueId, fields) {
    return await updateIssueFields(issueId, fields);
  }

  /**
   * Check if a field was updated in the changelog
   * @param {Object} changelog - The changelog object from the event
   * @param {Object} field - The field object to check
   * @returns {boolean} True if field was updated, false otherwise
   */
  isFieldUpdated(changelog, field) {
    return isFieldUpdated(changelog, field);
  }


  /**
   * Extract field value from provided issue object
   * @param {Object} issue - The complete issue object
   * @param {Object} field - Field object to extract
   * @returns {string|null} Field value or null if not found/empty
   */
  extractFieldValueFromIssue(issue, field) {
    if (!issue || !issue.fields) {
      console.error('Could not fetch issue details');
      return null;
    }

    const fieldValue = issue.fields[field.id] || '';
    
    if (!fieldValue || fieldValue.trim() === '') {
      console.error(`${field.name} field is empty`);
      return null;
    }

    return fieldValue;
  }

  /**
   * Get all issue types available for a specific project
   * @param {string} projectId - The project ID
   * @returns {Promise<Object[]>} Array of issue type objects with id, name, and description
   */
  async getProjectIssueTypes(projectId) {
    return await getProjectIssueTypes(projectId);
  }

  /**
   * Get paragraph fields available for a specific project and issue type
   * @param {string} projectId - Project ID for context-specific field resolution
   * @param {string} issueTypeId - Issue type ID for context-specific field resolution
   * @returns {Promise<Object[]>} Array of paragraph field objects, or empty array if failed
   */
  async getParagraphFieldsForProjectAndIssueType(projectId, issueTypeId) {
    return await getParagraphFieldsForProjectAndIssueType(projectId, issueTypeId);
  }

  /**
   * Get project information including key and name
   * @param {string} projectId - The project ID
   * @returns {Promise<Object|null>} Project object with id, key, name, etc. or null if not found
   */
  async getProject(projectId) {
    return await getProject(projectId);
  }

  /**
   * Get paragraph fields for all issue types in a project with a single API call
   * @param {string} projectId - Project ID
   * @param {string} projectKey - Project key (required for the bulk API)
   * @param {Object[]} issueTypes - Array of issue type objects with id and name
   * @returns {Promise<Object>} Object with issue type IDs as keys and paragraph fields arrays as values
   */
  async getParagraphFieldsForAllIssueTypes(projectId, projectKey, issueTypes) {
    return await getParagraphFieldsForAllIssueTypes(projectId, projectKey, issueTypes);
  }

  /**
   * Resolve output field for issue type with fallback logic
   * @param {string} issueTypeName - Issue type name
   * @param {string} projectId - Project ID for context-specific field resolution
   * @param {string} issueTypeId - Issue type ID for context-specific field resolution
   * @returns {Promise<Object|null>} Field object or null if not found
   */
  async resolveOutputFieldForIssueType(issueTypeName, projectId, issueTypeId) {
    const settingsService = new SettingsService();
    
    try {
      // First try to get the configured output field for this issue type
      const configuredFieldName = await settingsService.getOutputFieldForIssueType(issueTypeName, projectId);
      
      if (configuredFieldName) {
        const configuredField = await this.getFieldsByName(configuredFieldName, projectId, issueTypeId);
        if (configuredField) {
          console.log(`Using configured output field "${configuredFieldName}" for ${issueTypeName}`);
          return configuredField;
        } else {
          console.warn(`Configured output field "${configuredFieldName}" not found for ${issueTypeName}, falling back to defaults`);
        }
      }

      // Fallback 1: Try "AI Requirements" field (default)
      const aiRequirementsField = await this.getFieldsByName(config.fields.aiRequirements, projectId, issueTypeId);
      if (aiRequirementsField) {
        console.log(`Using default "${config.fields.aiRequirements}" field for ${issueTypeName}`);
        return aiRequirementsField;
      }

      // Fallback 2: Try "Description" field (universal fallback)
      const descriptionField = await this.getFieldsByName('Description', projectId, issueTypeId);
      if (descriptionField) {
        console.log(`Using "Description" fallback field for ${issueTypeName}`);
        return descriptionField;
      }

      console.error(`No suitable output field found for issue type ${issueTypeName}`);
      return null;
    } catch (error) {
      console.error(`Error resolving output field for ${issueTypeName}:`, error);
      return null;
    }
  }
}