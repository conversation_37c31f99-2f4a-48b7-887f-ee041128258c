/**
 * Requirements Service - Processes and converts requirements to ADF format
 */

import { markdownToAdf } from 'marklassian';
import { config } from '../config/index.js';

export class RequirementsService {

  /**
   * Process raw requirements: validate, clean, and convert to ADF
   * @param {string} rawRequirements - Raw requirements from AI service
   * @returns {Object} ADF formatted content
   * @throws {Error} If requirements are invalid
   */
  processRequirements(rawRequirements) {
    if (!this.validateRequirements(rawRequirements)) {
      throw new Error('Invalid requirements: must be a non-empty string');
    }

    const cleanedRequirements = this.cleanMarkdownResponse(rawRequirements);
    return markdownToAdf(cleanedRequirements);
  }

  /**
   * Clean up OpenRouter response by removing code block wrapping if present
   * @param {string} content - The raw content from OpenRouter
   * @returns {string} The cleaned content without code block wrapping
   */
  cleanMarkdownResponse(content) {
    if (!content) return content;

    const markdownBlockPattern = config.patterns.markdownCodeBlock;
    const match = content.match(markdownBlockPattern);

    if (match) {
      return match[1].trim();
    }

    return content;
  }

  /**
   * Validate requirements input
   * @param {string} requirements - Requirements text to validate
   * @returns {boolean} True if valid
   */
  validateRequirements(requirements) {
    return Boolean(requirements && typeof requirements === 'string' && requirements.trim() !== '');
  }

  /**
   * Create a loading message in ADF format
   * @returns {Object} ADF formatted loading message with italic text
   */
  createLoadingMessage() {
    return markdownToAdf('*Requirements are being generated...*');
  }
}