/**
 * Settings Service - Manages project-scoped templates and AI configuration
 */

import { kvs, WhereConditions } from '@forge/kvs';
import { config } from '../config/index.js';
import { getDefaultTemplate } from '../../build/system-prompt.js';

export class SettingsService {

  getProjectKey(baseKey, projectId) {
    if (!projectId) {
      throw new Error('Project ID is required for project-scoped storage');
    }
    return `project-${projectId}-${baseKey}`;
  }

  /**
   * Get all settings from storage with fallbacks to defaults
   * @param {string} projectId - Project ID for project-scoped settings
   * @returns {Promise<Object>} Settings object
   */
  async getSettings(projectId) {
    try {
      const [aiModel, temperature, maxTokens] = await Promise.all([
        kvs.get(this.getProjectKey(config.storage.keys.aiModel, projectId)),
        kvs.get(this.getProjectKey(config.storage.keys.temperature, projectId)),
        kvs.get(this.getProjectKey(config.storage.keys.maxTokens, projectId))
      ]);

      return {
        aiModel: aiModel || config.openrouter.model,
        temperature: temperature !== undefined ? temperature : config.openrouter.temperature,
        maxTokens: maxTokens || config.openrouter.maxTokens
      };
    } catch (error) {
      console.error('Failed to load settings:', error);
      return this.getDefaultSettings();
    }
  }

  /**
   * Save settings to storage
   * @param {Object} settings - Settings object to save
   * @param {string} projectId - Project ID for project-scoped settings
   * @returns {Promise<boolean>} Success status
   */
  async saveSettings(settings, projectId) {
    try {
      const savePromises = [];

      if (settings.aiModel !== undefined) {
        savePromises.push(
          kvs.set(this.getProjectKey(config.storage.keys.aiModel, projectId), settings.aiModel)
        );
      }

      if (settings.temperature !== undefined) {
        savePromises.push(
          kvs.set(this.getProjectKey(config.storage.keys.temperature, projectId), parseFloat(settings.temperature))
        );
      }

      if (settings.maxTokens !== undefined) {
        savePromises.push(
          kvs.set(this.getProjectKey(config.storage.keys.maxTokens, projectId), parseInt(settings.maxTokens))
        );
      }

      await Promise.all(savePromises);
      return true;
    } catch (error) {
      console.error('Failed to save settings:', error);
      return false;
    }
  }

  /**
   * Reset all settings to defaults
   * @param {string} projectId - Project ID for project-scoped settings
   * @returns {Promise<Object>} Default settings object
   */
  async resetToDefaults(projectId) {
    try {
      await Promise.all([
        kvs.delete(this.getProjectKey(config.storage.keys.aiModel, projectId)),
        kvs.delete(this.getProjectKey(config.storage.keys.temperature, projectId)),
        kvs.delete(this.getProjectKey(config.storage.keys.maxTokens, projectId))
      ]);

      return this.getDefaultSettings();
    } catch (error) {
      console.error('Failed to reset settings:', error);
      throw error;
    }
  }


  /**
   * Get AI configuration from storage with fallbacks
   * @param {string} projectId - Project ID for project-scoped settings
   * @returns {Promise<Object>} AI configuration object
   */
  async getAIConfig(projectId) {
    try {
      const [aiModel, temperature, maxTokens] = await Promise.all([
        kvs.get(this.getProjectKey(config.storage.keys.aiModel, projectId)),
        kvs.get(this.getProjectKey(config.storage.keys.temperature, projectId)),
        kvs.get(this.getProjectKey(config.storage.keys.maxTokens, projectId))
      ]);

      return {
        model: aiModel || config.openrouter.model,
        temperature: temperature !== undefined ? temperature : config.openrouter.temperature,
        maxTokens: maxTokens || config.openrouter.maxTokens
      };
    } catch (error) {
      // In test environment or when Forge runtime not available, use defaults from config
      return {
        model: config.openrouter.model,
        temperature: config.openrouter.temperature,
        maxTokens: config.openrouter.maxTokens
      };
    }
  }

  /**
   * Get default settings
   * @returns {Object} Default settings object
   */
  getDefaultSettings() {
    return {
      aiModel: config.openrouter.model,
      temperature: config.openrouter.temperature.toString(),
      maxTokens: config.openrouter.maxTokens.toString()
    };
  }

  /**
   * Get template for specific issue type
   * @param {string} issueType - Issue type (story, task, bug, or any custom type)
   * @param {string} projectId - Project ID for project-scoped templates
   * @returns {Promise<string>} Template content for the issue type
   */
  async getTemplateForIssueType(issueType, projectId) {
    try {
      const normalizedType = this.normalizeIssueType(issueType);
      if (!normalizedType) {
        throw new Error(`Invalid issue type: ${issueType}`);
      }

      const storageKey = this.getTemplateStorageKey(normalizedType, projectId);
      const storedTemplate = await kvs.get(storageKey);
      
      if (storedTemplate) {
        return storedTemplate;
      }
      return this.getDefaultTemplateForIssueType(normalizedType);
    } catch (error) {
      console.error(`Failed to load template for ${issueType}:`, error);
      return null;
    }
  }

  /**
   * Save template for specific issue type
   * @param {string} issueType - Issue type (story, task, bug, or any custom type)
   * @param {string} template - Template content
   * @param {string} projectId - Project ID for project-scoped templates
   * @returns {Promise<boolean>} Success status
   */
  async saveTemplateForIssueType(issueType, template, projectId) {
    try {
      const normalizedType = this.normalizeIssueType(issueType);
      if (!normalizedType) {
        throw new Error(`Invalid issue type: ${issueType}`);
      }

      const storageKey = this.getTemplateStorageKey(normalizedType, projectId);
      await kvs.set(storageKey, template);
      return true;
    } catch (error) {
      console.error(`Failed to save template for ${issueType}:`, error);
      return false;
    }
  }


  /**
   * Get templates for a specific list of issue types
   * @param {string[]} issueTypes - Array of issue type names
   * @param {string} projectId - Project ID for project-scoped templates
   * @returns {Promise<Object>} Object with templates keyed by issue type
   */
  async getTemplatesForIssueTypes(issueTypes, projectId) {
    try {
      const templatePromises = issueTypes.map(issueType => 
        this.getTemplateForIssueType(issueType, projectId)
      );
      
      const templates = await Promise.all(templatePromises);
      
      const result = {};
      issueTypes.forEach((issueType, index) => {
        result[issueType] = templates[index];
      });
      
      return result;
    } catch (error) {
      console.error('Failed to load templates:', error);
      throw error;
    }
  }

  /**
   * Save multiple templates at once
   * @param {Object} templates - Object with templates keyed by issue type
   * @param {string} projectId - Project ID for project-scoped templates
   * @returns {Promise<boolean>} Success status
   */
  async saveAllTemplates(templates, projectId) {
    try {
      const savePromises = [];

      for (const [issueType, template] of Object.entries(templates)) {
        if (template !== undefined) {
          savePromises.push(this.saveTemplateForIssueType(issueType, template, projectId));
        }
      }

      const results = await Promise.all(savePromises);
      return results.every(result => result === true);
    } catch (error) {
      console.error('Failed to save templates:', error);
      return false;
    }
  }

  /**
   * Delete template for a specific issue type
   * @param {string} issueType - Issue type to delete template for
   * @param {string} projectId - Project ID for project-scoped templates
   * @returns {Promise<boolean>} Success status
   */
  async deleteTemplateForIssueType(issueType, projectId) {
    try {
      const normalizedType = this.normalizeIssueType(issueType);
      if (!normalizedType) {
        throw new Error(`Invalid issue type: ${issueType}`);
      }

      const storageKey = this.getTemplateStorageKey(normalizedType, projectId);
      await kvs.delete(storageKey);
      return true;
    } catch (error) {
      console.error(`Failed to delete template for ${issueType}:`, error);
      return false;
    }
  }

  /**
   * Reset all templates to defaults
   * @param {string} projectId - Project ID for project-scoped templates
   * @returns {Promise<Object>} Empty object (templates will be loaded dynamically as needed)
   */
  async resetTemplatesToDefaults(projectId) {
    try {
      const allTemplates = await this.getAllTemplates(projectId);
      const templateKeys = Object.keys(allTemplates);
      
      const deletePromises = [];
      for (const issueType of templateKeys) {
        const storageKey = this.getTemplateStorageKey(issueType, projectId);
        deletePromises.push(kvs.delete(storageKey));
      }
      
      await Promise.all(deletePromises);
      
      return {};
    } catch (error) {
      console.error('Failed to reset templates:', error);
      throw error;
    }
  }

  /**
   * Normalize issue type name to lowercase and replace spaces with hyphens
   * @param {string} issueType - Issue type name
   * @returns {string} Normalized issue type
   */
  normalizeIssueType(issueType) {
    if (!issueType) return null;
    
    // Convert to lowercase and replace spaces with hyphens for storage consistency
    return issueType.toLowerCase().replace(/\s+/g, '-');
  }

  /**
   * Get storage key for issue type template
   * @param {string} issueType - Normalized issue type
   * @param {string} projectId - Project ID for project-scoped storage
   * @returns {string} Storage key for the issue type template
   */
  getTemplateStorageKey(issueType, projectId) {
    const baseKey = `${config.storage.keys.templatePrefix}${issueType}`;
    return this.getProjectKey(baseKey, projectId);
  }


  /**
   * Get default template for specific issue type
   * @param {string} issueType - Issue type (story, task, bug, epic, or any custom type)
   * @returns {string} Default template content
   */
  getDefaultTemplateForIssueType(issueType) {
    const normalizedType = this.normalizeIssueType(issueType);
    
    // Use the dynamic template system from build
    return getDefaultTemplate(normalizedType);
  }

  /**
   * Get all stored templates from storage
   * @param {string} projectId - Project ID for project-scoped templates
   * @returns {Promise<Object>} Object with all stored templates keyed by issue type
   */
  async getAllTemplates(projectId) {
    try {
      const projectTemplatePrefix = this.getProjectKey(config.storage.keys.templatePrefix, projectId);
      
      const queryResult = await kvs.query()
        .where('key', WhereConditions.beginsWith(projectTemplatePrefix))
        .getMany();
      
      const templates = {};
      
      if (queryResult && queryResult.results && Array.isArray(queryResult.results)) {
        for (const result of queryResult.results) {
          const issueType = result.key.replace(projectTemplatePrefix, '');
          templates[issueType] = result.value;
        }
      }
      
      return templates;
    } catch (error) {
      console.error('Failed to load templates:', error);
      return {};
    }
  }

  /**
   * Get output field configuration for specific issue type
   * @param {string} issueType - Issue type (story, task, bug, or any custom type)
   * @param {string} projectId - Project ID for project-scoped storage
   * @returns {Promise<string|null>} Configured output field name or null if not configured
   */
  async getOutputFieldForIssueType(issueType, projectId) {
    try {
      const normalizedType = this.normalizeIssueType(issueType);
      if (!normalizedType) {
        throw new Error(`Invalid issue type: ${issueType}`);
      }

      const storageKey = this.getOutputFieldStorageKey(normalizedType, projectId);
      const storedFieldName = await kvs.get(storageKey);
      
      return storedFieldName || null;
    } catch (error) {
      console.error(`Failed to load output field for ${issueType}:`, error);
      return null;
    }
  }

  /**
   * Save output field configuration for specific issue type
   * @param {string} issueType - Issue type (story, task, bug, or any custom type)
   * @param {string} fieldName - Field name to use for output
   * @param {string} projectId - Project ID for project-scoped storage
   * @returns {Promise<boolean>} Success status
   */
  async saveOutputFieldForIssueType(issueType, fieldName, projectId) {
    try {
      const normalizedType = this.normalizeIssueType(issueType);
      if (!normalizedType) {
        throw new Error(`Invalid issue type: ${issueType}`);
      }

      const storageKey = this.getOutputFieldStorageKey(normalizedType, projectId);
      await kvs.set(storageKey, fieldName);
      return true;
    } catch (error) {
      console.error(`Failed to save output field for ${issueType}:`, error);
      return false;
    }
  }

  /**
   * Get output field configurations for multiple issue types
   * @param {string[]} issueTypes - Array of issue type names
   * @param {string} projectId - Project ID for project-scoped storage
   * @returns {Promise<Object>} Object with output field names keyed by issue type
   */
  async getOutputFieldsForIssueTypes(issueTypes, projectId) {
    try {
      const fieldPromises = issueTypes.map(issueType => 
        this.getOutputFieldForIssueType(issueType, projectId)
      );
      
      const fields = await Promise.all(fieldPromises);
      
      const result = {};
      issueTypes.forEach((issueType, index) => {
        result[issueType] = fields[index];
      });
      
      return result;
    } catch (error) {
      console.error('Failed to load output fields:', error);
      throw error;
    }
  }

  /**
   * Delete output field configuration for a specific issue type
   * @param {string} issueType - Issue type to delete output field for
   * @param {string} projectId - Project ID for project-scoped storage
   * @returns {Promise<boolean>} Success status
   */
  async deleteOutputFieldForIssueType(issueType, projectId) {
    try {
      const normalizedType = this.normalizeIssueType(issueType);
      if (!normalizedType) {
        throw new Error(`Invalid issue type: ${issueType}`);
      }

      const storageKey = this.getOutputFieldStorageKey(normalizedType, projectId);
      await kvs.delete(storageKey);
      return true;
    } catch (error) {
      console.error(`Failed to delete output field for ${issueType}:`, error);
      return false;
    }
  }

  /**
   * Get storage key for issue type output field configuration
   * @param {string} issueType - Normalized issue type
   * @param {string} projectId - Project ID for project-scoped storage
   * @returns {string} Storage key for the issue type output field
   */
  getOutputFieldStorageKey(issueType, projectId) {
    const baseKey = `${config.storage.keys.outputFieldPrefix}${issueType}`;
    return this.getProjectKey(baseKey, projectId);
  }


}