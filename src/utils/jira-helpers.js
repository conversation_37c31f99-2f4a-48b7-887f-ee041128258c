/**
 * <PERSON>ra Helper Functions for Forge App
 *
 * This module contains helper functions for interacting with <PERSON><PERSON> using the Forge API.
 * These functions are designed to work within the Forge runtime environment.
 */

import api, { route } from '@forge/api';

/**
 * Get all field information for a specific project and issue type using createmeta API
 * @param {string} projectId - Project ID for context-specific field resolution
 * @param {string} issueTypeId - Issue type ID for context-specific field resolution
 * @returns {Promise<Object[]>} Array of field objects, or empty array if failed
 */
export async function getAllFieldsForProjectAndIssueType(projectId, issueTypeId) {
  if (!projectId || !issueTypeId) {
    console.error('Project ID and Issue Type ID are required for context-specific field resolution');
    return [];
  }
  
  try {
    // Query the createmeta API to get fields for specific project/issue type context
    const fieldsResponse = await api.asApp().requestJira(route`/rest/api/3/issue/createmeta/${projectId}/issuetypes/${issueTypeId}`);
    if (!fieldsResponse.ok) {
      console.error(`Failed to fetch fields for project ${projectId}, issue type ${issueTypeId}: ${fieldsResponse.status} ${fieldsResponse.statusText}`);
      return [];
    }

    const createmetaData = await fieldsResponse.json();
    const fieldsData = createmetaData.fields || [];
    
    // Transform createmeta field format to match expected format
    const transformedFields = fieldsData.map(field => ({
      id: field.fieldId,
      name: field.name,
      key: field.key,
      required: field.required,
      operations: field.operations,
      schema: field.schema || { type: 'unknown' }
    }));

    return transformedFields;
  } catch (error) {
    console.error(`Error fetching fields for project ${projectId}, issue type ${issueTypeId}:`, error);
    return [];
  }
}


/**
 * Update a Jira issue with the given fields using Forge API
 * @param {string} issueId - The issue ID
 * @param {Object} fields - Object containing field IDs as keys and values to update
 * @returns {Promise<boolean>} True if successful, false otherwise
 */
export async function updateIssueFields(issueId, fields) {
  try {
    const updateResponse = await api.asApp().requestJira(route`/rest/api/3/issue/${issueId}`, {
      method: 'PUT',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ fields })
    });

    if (!updateResponse.ok) {
      const errorBody = await updateResponse.text();
      console.error(`Failed to update issue ${issueId}: ${updateResponse.status} ${updateResponse.statusText}`);
      console.error('Update Error Body:', errorBody);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`Error updating issue ${issueId}:`, error);
    return false;
  }
}

/**
 * Check if a specific field was updated in the changelog
 * @param {Object} changelog - The changelog object from the event
 * @param {Object} field - The field object with id and name
 * @returns {boolean} True if the field was updated
 */
export function isFieldUpdated(changelog, field) {
  if (!changelog || !changelog.items || !field) {
    return false;
  }

  return changelog.items.some(item =>
    item.fieldId === field.id || item.field === field.name
  );
}


/**
 * Get issue details including all custom fields
 * @param {string} issueIdOrKey - The issue ID or key
 * @returns {Promise<Object|null>} Issue object or null if not found
 */
export async function getIssue(issueIdOrKey) {
  try {
    const response = await api.asApp().requestJira(route`/rest/api/3/issue/${issueIdOrKey}`);
    if (!response.ok) {
      console.error(`Failed to fetch issue ${issueIdOrKey}: ${response.status} ${response.statusText}`);
      return null;
    }
    
    const issue = await response.json();
    return issue;
  } catch (error) {
    console.error(`Error fetching issue ${issueIdOrKey}:`, error);
    return null;
  }
}

/**
 * Get paragraph fields available for a specific project and issue type
 * @param {string} projectId - Project ID for context-specific field resolution
 * @param {string} issueTypeId - Issue type ID for context-specific field resolution
 * @returns {Promise<Object[]>} Array of paragraph field objects, or empty array if failed
 */
export async function getParagraphFieldsForProjectAndIssueType(projectId, issueTypeId) {
  const allFields = await getAllFieldsForProjectAndIssueType(projectId, issueTypeId);

  // Filter for paragraph fields (rich text fields)
  const paragraphFields = allFields.filter(field => {
    if (!field.schema) return false;

    const schema = field.schema;

    // Must be a string type field
    if (schema.type !== 'string') return false;

    // Check for definitive paragraph field indicators
    return (
      // System description field
      schema.system === 'description' ||
      // Custom textarea fields (paragraph fields)
      schema.custom === 'com.atlassian.jira.plugin.system.customfieldtypes:textarea'
    );
  });

  console.log(`Found ${paragraphFields.length} paragraph fields for project ${projectId}, issue type ${issueTypeId}`);
  return paragraphFields;
}

/**
 * Get paragraph fields for all issue types in a project with a single API call
 * @param {string} projectId - Project ID
 * @param {string} projectKey - Project key (required for the bulk API)
 * @param {Object[]} issueTypes - Array of issue type objects with id and name
 * @returns {Promise<Object>} Object with issue type IDs as keys and paragraph fields arrays as values
 */
export async function getParagraphFieldsForAllIssueTypes(projectId, projectKey, issueTypes) {
  if (!projectId || !projectKey || !issueTypes || issueTypes.length === 0) {
    console.error('Project ID, project key, and issue types are required for bulk field resolution');
    return {};
  }

  try {
    // Use the bulk createmeta API to get fields for all issue types in one call
    const fieldsResponse = await api.asApp().requestJira(
      route`/rest/api/3/issue/createmeta?projectKeys=${projectKey}&expand=projects.issuetypes.fields`
    );

    if (!fieldsResponse.ok) {
      console.error(`Failed to fetch bulk fields for project ${projectKey}: ${fieldsResponse.status} ${fieldsResponse.statusText}`);
      return {};
    }

    const createmetaData = await fieldsResponse.json();
    const projects = createmetaData.projects || [];

    if (projects.length === 0) {
      console.warn(`No projects found in createmeta response for project ${projectKey}`);
      return {};
    }

    const project = projects[0]; // Should be our target project
    const issueTypesData = project.issuetypes || [];

    const result = {};

    // Process each issue type and extract paragraph fields
    for (const issueTypeData of issueTypesData) {
      const fieldsData = issueTypeData.fields || {};

      // Transform and filter fields
      const allFields = Object.values(fieldsData).map(field => ({
        id: field.fieldId,
        name: field.name,
        key: field.key,
        required: field.required,
        operations: field.operations,
        schema: field.schema || { type: 'unknown' }
      }));

      // Filter for paragraph fields
      const paragraphFields = allFields.filter(field => {
        if (!field.schema) return false;

        const schema = field.schema;

        // Must be a string type field
        if (schema.type !== 'string') return false;

        // Check for definitive paragraph field indicators
        return (
          // System description field
          schema.system === 'description' ||
          // Custom textarea fields (paragraph fields)
          schema.custom === 'com.atlassian.jira.plugin.system.customfieldtypes:textarea'
        );
      });

      result[issueTypeData.id] = paragraphFields;
      console.log(`Found ${paragraphFields.length} paragraph fields for issue type ${issueTypeData.name} (${issueTypeData.id})`);
    }

    return result;
  } catch (error) {
    console.error(`Error fetching bulk fields for project ${projectKey}:`, error);
    return {};
  }
}

/**
 * Get project information including key and name
 * @param {string} projectId - The project ID
 * @returns {Promise<Object|null>} Project object with id, key, name, etc. or null if not found
 */
export async function getProject(projectId) {
  if (!projectId) {
    console.error('Project ID is required to get project information');
    return null;
  }

  try {
    const response = await api.asApp().requestJira(route`/rest/api/3/project/${projectId}`);
    if (!response.ok) {
      console.error(`Failed to fetch project ${projectId}: ${response.status} ${response.statusText}`);
      return null;
    }

    const project = await response.json();
    return {
      id: project.id,
      key: project.key,
      name: project.name,
      description: project.description || '',
      projectTypeKey: project.projectTypeKey || '',
      simplified: project.simplified || false
    };
  } catch (error) {
    console.error(`Error fetching project ${projectId}:`, error);
    return null;
  }
}

/**
 * Get all issue types available for a specific project
 * @param {string} projectId - The project ID
 * @returns {Promise<Object[]>} Array of issue type objects with id, name, and description
 */
export async function getProjectIssueTypes(projectId) {
  if (!projectId) {
    console.error('Project ID is required to get issue types');
    return [];
  }

  try {
    const response = await api.asApp().requestJira(route`/rest/api/3/issuetype/project?projectId=${projectId}`);
    if (!response.ok) {
      console.error(`Failed to fetch issue types for project ${projectId}: ${response.status} ${response.statusText}`);
      return [];
    }

    const issueTypes = await response.json();

    // Transform the response to a more usable format
    const transformedIssueTypes = issueTypes.map(issueType => ({
      id: issueType.id,
      name: issueType.name,
      description: issueType.description || '',
      iconUrl: issueType.iconUrl || '',
      subtask: issueType.subtask || false
    }));

    console.log(`Retrieved ${transformedIssueTypes.length} issue types for project ${projectId}`);
    return transformedIssueTypes;
  } catch (error) {
    console.error(`Error fetching issue types for project ${projectId}:`, error);
    return [];
  }
}

