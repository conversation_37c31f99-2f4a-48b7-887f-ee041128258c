/**
 * OpenRouter API Client
 * 
 * This module provides a pure HTTP client for interacting with the OpenRouter API.
 * It accepts fully formed prompts and returns AI responses.
 */

import api from '@forge/api';
import { config } from '../config/index.js';

// Configuration is now centralized in config/index.js

/**
 * Call OpenRouter API with a fully formed prompt
 * @param {string} prompt - The complete prompt for the AI
 * @param {Object} options - Optional configuration overrides
 * @returns {Promise<string|null>} The AI response or null if failed
 */
export async function callOpenRouter(prompt, options = {}) {
  const openrouterConfig = {
    model: options.model || config.openrouter.model,
    temperature: options.temperature || config.openrouter.temperature,
    maxTokens: options.maxTokens || config.openrouter.maxTokens
  };
  const apiKey = config.openrouter.apiKey;

  if (!apiKey) {
    console.error(`OpenRouter API Key (${config.env.openrouterApiKey}) is not set in Forge environment variables.`);
    return null;
  }

  if (!prompt || prompt.trim() === '') {
    console.error('Prompt is required and cannot be empty');
    return null;
  }

  try {
    console.log(`Calling OpenRouter API with ${openrouterConfig.model}`);

    // Make the API request (timeout handled by Forge async events)
    const response = await api.fetch(config.openrouter.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': config.openrouter.headers.httpReferer,
        'X-Title': config.openrouter.headers.xTitle,
      },
      body: JSON.stringify({
        model: openrouterConfig.model,
        messages: [
          { role: config.chat.roles.user, content: prompt },
        ],
        temperature: openrouterConfig.temperature,
        max_tokens: openrouterConfig.maxTokens,
      }),
    });

    if (!response.ok) {
      const errorBody = await response.text();
      console.error(`OpenRouter API request failed with ${response.status} ${response.statusText}`);
      console.error('OpenRouter Error Body:', errorBody);
      return null;
    }

    const data = await response.json();

    if (data.choices && data.choices.length > 0 && data.choices[0].message) {
      const rawContent = data.choices[0].message.content.trim();
      console.log('OpenRouter Response Received:', rawContent);
      return rawContent;
    } else {
      console.error('OpenRouter response format unexpected or empty.', JSON.stringify(data));
      return null;
    }
  } catch (error) {
    console.error(`Error calling OpenRouter API:`, error);
    return null;
  }
}