/* Atlassian Design System styles for Admin Page using @atlaskit/tokens */

body {
    margin: 0;
    padding: 0;
    background: var(--ds-background-neutral, #F4F5F7);
}

.admin-container {
    padding: var(--ds-space-400, 32px);
    font-family: var(--ds-font-family-sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif);
    max-width: 960px;
    background: var(--ds-surface, #FFFFFF);
    min-height: 100vh;
    line-height: 1.4;
    margin: 0 auto;
}

.header-section {
    margin-bottom: var(--ds-space-200, 16px);
    padding-top: var(--ds-space-100, 8px);
}

.main-title {
    color: var(--ds-text, #172B4D);
    font-size: 28px;
    font-weight: 500;
    margin: 0 0 var(--ds-space-100, 8px) 0;
    letter-spacing: -0.01em;
}

.subtitle {
    color: var(--ds-text-subtlest, #6B778C);
    font-size: 14px;
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
}

.template-card {
    background: var(--ds-background-neutral-subtle, #FAFBFC);
    border: 2px solid var(--ds-border, #DFE1E6);
    border-radius: var(--ds-border-radius-200, 8px);
    padding: var(--ds-space-300, 24px);
    margin-bottom: var(--ds-space-300, 24px);
    box-shadow: var(--ds-shadow-raised, 0 1px 1px rgba(9, 30, 66, 0.25));
}

.card-title {
    margin: 0 0 var(--ds-space-200, 16px) 0;
    color: var(--ds-text, #172B4D);
    font-size: 20px;
    font-weight: 500;
}

.card-description {
    color: var(--ds-text-subtlest, #6B778C);
    font-size: 14px;
    margin: 0 0 var(--ds-space-200, 16px) 0;
}

.template-textarea {
    width: calc(100% - var(--ds-space-300, 24px));
    padding: var(--ds-space-150, 12px);
    border: 2px solid var(--ds-border-input, #DFE1E6);
    border-radius: var(--ds-border-radius-100, 6px);
    font-family: var(--ds-font-family-sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif);
    font-size: 14px;
    line-height: 1.4;
    background: var(--ds-surface, #FFFFFF);
    color: var(--ds-text, #172B4D);
    resize: vertical;
    transition: border-color 0.2s ease;
}

.template-textarea:focus {
    outline: none;
    border-color: var(--ds-border-focused, #0052CC);
    box-shadow: 0 0 0 2px var(--ds-border-focused, rgba(0, 82, 204, 0.2));
}

.button-group {
    display: flex;
    gap: var(--ds-space-150, 12px);
    margin-bottom: var(--ds-space-300, 24px);
}

.btn-primary {
    background: var(--ds-background-brand-bold, #0052CC);
    color: var(--ds-text-inverse, white);
    border: none;
    padding: var(--ds-space-150, 12px) var(--ds-space-300, 24px);
    border-radius: var(--ds-border-radius-100, 6px);
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: background-color 0.2s ease;
    box-shadow: var(--ds-shadow-raised, 0 1px 1px rgba(9, 30, 66, 0.25));
}

.btn-primary:hover {
    background-color: var(--ds-background-brand-bold-hovered, #0065FF);
}

.btn-primary:active {
    background-color: var(--ds-background-brand-bold-pressed, #003884);
}

.btn-primary:disabled {
    background-color: var(--ds-background-disabled, #DFE1E6);
    color: var(--ds-text-disabled, #6B778C);
    cursor: not-allowed;
}

.btn-secondary {
    background: var(--ds-surface, #FFFFFF);
    color: var(--ds-text-subtle, #42526E);
    border: 2px solid var(--ds-border, #DFE1E6);
    padding: 10px 22px;
    border-radius: var(--ds-border-radius-100, 6px);
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: var(--ds-shadow-raised, 0 1px 1px rgba(9, 30, 66, 0.25));
}

.btn-secondary:hover {
    background-color: var(--ds-background-neutral-subtle-hovered, #F4F5F7);
    border-color: var(--ds-border-bold, #C1C7D0);
}

.btn-secondary:active {
    background-color: var(--ds-background-neutral-subtle-pressed, #E4E5EA);
}

.status-message {
    margin-top: var(--ds-space-200, 16px);
}

.status-success {
    background: var(--ds-background-success, #E3FCEF);
    border: 2px solid var(--ds-border-success, #ABF5D1);
    color: var(--ds-text-success, #006644);
    padding: var(--ds-space-200, 16px);
    border-radius: var(--ds-border-radius-100, 6px);
    font-size: 14px;
    font-weight: 500;
    box-shadow: var(--ds-shadow-raised, 0 1px 1px rgba(9, 30, 66, 0.15));
}

.status-info {
    background: var(--ds-background-information, #DEEBFF);
    border: 2px solid var(--ds-border-information, #B3D4FF);
    color: var(--ds-text-information, #0052CC);
    padding: var(--ds-space-200, 16px);
    border-radius: var(--ds-border-radius-100, 6px);
    font-size: 14px;
    font-weight: 500;
    box-shadow: var(--ds-shadow-raised, 0 1px 1px rgba(9, 30, 66, 0.15));
}

.status-error {
    background: var(--ds-background-danger, #FFEBE6);
    border: 2px solid var(--ds-border-danger, #FFBDAD);
    color: var(--ds-text-danger, #DE350B);
    padding: var(--ds-space-200, 16px);
    border-radius: var(--ds-border-radius-100, 6px);
    font-size: 14px;
    font-weight: 500;
    box-shadow: var(--ds-shadow-raised, 0 1px 1px rgba(9, 30, 66, 0.15));
}

/* Issue type selection */
.template-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: var(--ds-space-200, 16px);
    gap: var(--ds-space-300, 24px);
}

.control-group {
    display: flex;
    align-items: center;
    gap: var(--ds-space-100, 8px);
    justify-content: flex-start;
}

.issue-type-section {
    display: flex;
    align-items: center;
    gap: var(--ds-space-100, 8px);
    justify-content: flex-start;
}

.field-help {
    color: var(--ds-text-subtlest, #6B778C);
    font-size: 12px;
    margin: 0;
    line-height: 1.3;
}

.issue-type-label {
    color: var(--ds-text-subtle, #42526E);
    font-weight: bold;
    font-size: 16px;
    white-space: nowrap;
}

.issue-type-select {
    padding: var(--ds-space-100, 8px) var(--ds-space-150, 12px);
    border: 2px solid var(--ds-border-input, #DFE1E6);
    border-radius: var(--ds-border-radius-100, 6px);
    background: var(--ds-surface, #FFFFFF);
    color: var(--ds-text, #172B4D);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: border-color 0.2s ease;
    min-width: 120px;
}

.issue-type-select:focus {
    outline: none;
    border-color: var(--ds-border-focused, #0052CC);
    box-shadow: 0 0 0 2px var(--ds-border-focused, rgba(0, 82, 204, 0.2));
}

.issue-type-select:hover {
    border-color: var(--ds-border-bold, #C1C7D0);
}


/* Unsaved changes warning */
.unsaved-warning {
    background: var(--ds-background-warning, #FFF4E6);
    border: 2px solid var(--ds-border-warning, #FFC400);
    color: var(--ds-text-warning, #B25D00);
    padding: var(--ds-space-150, 12px);
    border-radius: var(--ds-border-radius-100, 6px);
    font-size: 14px;
    font-weight: 500;
    margin-top: var(--ds-space-150, 12px);
    margin-bottom: var(--ds-space-150, 12px);
    display: flex;
    align-items: center;
    gap: var(--ds-space-100, 8px);
}

/* Danger button for reset all */
.btn-danger {
    background: var(--ds-background-danger-bold, #DE350B);
    color: var(--ds-text-inverse, white);
    border: none;
    padding: var(--ds-space-150, 12px) var(--ds-space-300, 24px);
    border-radius: var(--ds-border-radius-100, 6px);
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: background-color 0.2s ease;
    box-shadow: var(--ds-shadow-raised, 0 1px 1px rgba(9, 30, 66, 0.25));
}

.btn-danger:hover {
    background-color: var(--ds-background-danger-bold-hovered, #FF5630);
}

.btn-danger:active {
    background-color: var(--ds-background-danger-bold-pressed, #BF2600);
}

.btn-danger:disabled {
    background-color: var(--ds-background-disabled, #DFE1E6);
    color: var(--ds-text-disabled, #6B778C);
    cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
    .admin-container {
        padding: var(--ds-space-200, 16px);
    }
    
    .template-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--ds-space-200, 16px);
    }
    
    .control-group {
        justify-content: space-between;
    }
    
    .issue-type-section {
        justify-content: space-between;
    }
    
    .button-group {
        flex-direction: column;
        gap: var(--ds-space-100, 8px);
    }
    
    .btn-primary,
    .btn-secondary,
    .btn-danger {
        width: 100%;
        justify-content: center;
        display: flex;
    }
}

/* Loading state */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading-state {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-space-500, 40px);
    color: var(--ds-text-subtlest, #6B778C);
    font-size: 16px;
}

/* Editor container with MD Editor integration */
.editor-container {
    position: relative;
    border-radius: var(--ds-border-radius-100, 6px);
    overflow: hidden;
}

/* MD Editor Atlassian Design Integration */
.editor-container .w-md-editor {
    background: var(--ds-surface, #FFFFFF);
    border: 2px solid var(--ds-border, #DFE1E6);
    border-radius: var(--ds-border-radius-100, 6px);
    font-family: var(--ds-font-family-sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif);
}

.editor-container .w-md-editor:focus-within {
    border-color: var(--ds-border-focused, #0052CC);
    box-shadow: 0 0 0 2px var(--ds-border-focused, rgba(0, 82, 204, 0.2));
}

.editor-container .w-md-editor-text-pre,
.editor-container .w-md-editor-text-input {
    font-family: var(--ds-font-family-sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif) !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    color: var(--ds-text, #172B4D) !important;
}

/* Additional cursor positioning fixes for syntax highlighting */
.editor-container .w-md-editor-text pre > code,
.editor-container .w-md-editor-text pre > code *,
.editor-container .w-md-editor-text span.token.title.important,
.editor-container .w-md-editor-text span.token.title.important .token.punctuation,
.editor-container .w-md-editor .title {
    font-family: var(--ds-font-family-sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif) !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    font-weight: inherit !important;
    letter-spacing: normal !important;
}

/* Reset conflicting title styles */
.editor-container .w-md-editor .title {
    all: unset;
    display: inline;
    font: inherit !important;
}

.editor-container .w-md-editor-toolbar {
    background: var(--ds-background-neutral-subtle, #F4F5F7);
    border-bottom: 1px solid var(--ds-border, #DFE1E6);
    padding: var(--ds-space-100, 8px);
}

.editor-container .w-md-editor-toolbar button {
    color: var(--ds-text-subtle, #42526E);
    border-radius: var(--ds-border-radius-050, 3px);
    transition: background-color 0.2s ease;
}

.editor-container .w-md-editor-toolbar button:hover {
    background-color: var(--ds-background-neutral-subtle-hovered, #EBECF0);
    color: var(--ds-text, #172B4D);
}

.editor-container .w-md-editor-toolbar button.active {
    background-color: var(--ds-background-brand-bold, #0052CC);
    color: var(--ds-text-inverse, white);
}

/* Markdown preview styling */
.editor-container .wmde-markdown {
    font-family: var(--ds-font-family-sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif);
    font-size: 14px;
    line-height: 1.4;
    color: var(--ds-text, #172B4D);
}

.editor-container .wmde-markdown h1,
.editor-container .wmde-markdown h2,
.editor-container .wmde-markdown h3 {
    color: var(--ds-text, #172B4D);
    font-weight: 500;
}

.editor-container .wmde-markdown code {
    background: var(--ds-background-neutral-subtle, #F4F5F7);
    border: 1px solid var(--ds-border, #DFE1E6);
    border-radius: var(--ds-border-radius-050, 3px);
    padding: 2px 4px;
    font-size: 13px;
}

.editor-container .wmde-markdown pre {
    background: var(--ds-background-neutral-subtle, #F4F5F7);
    border: 1px solid var(--ds-border, #DFE1E6);
    border-radius: var(--ds-border-radius-100, 6px);
    padding: var(--ds-space-150, 12px);
}

/* Enhanced button states */
.btn-primary:focus,
.btn-secondary:focus,
.btn-danger:focus {
    outline: 2px solid var(--ds-border-focused, #0052CC);
    outline-offset: 2px;
}

/* Better spacing for components */
.template-header {
    margin-bottom: var(--ds-space-250, 20px);
}

.button-group {
    margin-top: var(--ds-space-200, 16px);
}

/* Tab Navigation Styles */
.tab-navigation {
    margin-bottom: var(--ds-space-300, 24px);
    border-bottom: 2px solid var(--ds-border, #DFE1E6);
}

.tab-list {
    display: flex;
    gap: 0;
    margin: 0;
    padding: 0;
}

.tab-button {
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--ds-text-subtlest, #6B778C);
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    padding: var(--ds-space-200, 16px) var(--ds-space-300, 24px);
    transition: all 0.2s ease;
    position: relative;
    outline: none;
}

.tab-button:hover {
    color: var(--ds-text, #172B4D);
    background-color: var(--ds-background-neutral-subtle, #F4F5F7);
}

.tab-button.active {
    color: var(--ds-text-brand, #0052CC);
    border-bottom-color: var(--ds-border-brand, #0052CC);
    background-color: var(--ds-surface, #FFFFFF);
}

.tab-button:focus {
    outline: none;
}

/* Settings Card Styles */
.settings-card {
    background: var(--ds-background-neutral-subtle, #FAFBFC);
    border: 2px solid var(--ds-border, #DFE1E6);
    border-radius: var(--ds-border-radius-200, 8px);
    padding: var(--ds-space-300, 24px);
    margin-bottom: var(--ds-space-300, 24px);
    box-shadow: var(--ds-shadow-raised, 0 1px 1px rgba(9, 30, 66, 0.25));
}

.settings-header {
    margin-bottom: var(--ds-space-300, 24px);
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: var(--ds-space-250, 20px);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--ds-space-100, 8px);
}

.form-label {
    color: var(--ds-text, #172B4D);
    font-weight: 500;
    font-size: 14px;
    margin-bottom: var(--ds-space-050, 4px);
}

.form-input {
    padding: var(--ds-space-150, 12px);
    border: 2px solid var(--ds-border-input, #DFE1E6);
    border-radius: var(--ds-border-radius-100, 6px);
    font-family: var(--ds-font-family-sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif);
    font-size: 14px;
    line-height: 1.4;
    background: var(--ds-surface, #FFFFFF);
    color: var(--ds-text, #172B4D);
    transition: border-color 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--ds-border-focused, #0052CC);
    box-shadow: 0 0 0 2px var(--ds-border-focused, rgba(0, 82, 204, 0.2));
}

.form-input:hover {
    border-color: var(--ds-border-bold, #C1C7D0);
}

.form-help {
    color: var(--ds-text-subtlest, #6B778C);
    font-size: 12px;
    margin: 0;
    line-height: 1.3;
}

/* Temperature Control */
.temperature-control {
    display: flex;
    align-items: center;
    gap: var(--ds-space-150, 12px);
}

.form-range {
    flex: 1;
    height: 6px;
    background: var(--ds-background-neutral, #DFE1E6);
    border-radius: var(--ds-border-radius-050, 3px);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--ds-background-brand-bold, #0052CC);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 82, 204, 0.3);
}

.form-range::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: var(--ds-background-brand-bold, #0052CC);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 82, 204, 0.3);
}

.temperature-input {
    width: 80px;
    text-align: center;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(9, 30, 66, 0.54);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: var(--ds-space-200, 16px);
}

.modal-content {
    background: var(--ds-surface, #FFFFFF);
    border-radius: var(--ds-border-radius-200, 8px);
    box-shadow: var(--ds-shadow-overlay, 0 8px 16px -4px rgba(9, 30, 66, 0.25), 0 0 0 1px rgba(9, 30, 66, 0.08));
    max-width: 400px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
}

.modal-header {
    padding: var(--ds-space-300, 24px) var(--ds-space-300, 24px) var(--ds-space-200, 16px);
    border-bottom: 1px solid var(--ds-border, #DFE1E6);
}

.modal-title {
    margin: 0;
    color: var(--ds-text, #172B4D);
    font-size: 20px;
    font-weight: 500;
}

.modal-body {
    padding: var(--ds-space-300, 24px);
}

.modal-message {
    margin: 0;
    color: var(--ds-text, #172B4D);
    font-size: 14px;
    line-height: 1.4;
}

.modal-footer {
    padding: var(--ds-space-200, 16px) var(--ds-space-300, 24px) var(--ds-space-300, 24px);
    display: flex;
    justify-content: flex-end;
    gap: var(--ds-space-150, 12px);
    border-top: 1px solid var(--ds-border, #DFE1E6);
}

/* Responsive Tab Navigation */
@media (max-width: 768px) {
    .tab-navigation {
        margin-bottom: var(--ds-space-200, 16px);
    }
    
    .tab-button {
        padding: var(--ds-space-150, 12px) var(--ds-space-200, 16px);
        font-size: 14px;
        flex: 1;
        text-align: center;
    }
    
    .settings-card {
        padding: var(--ds-space-200, 16px);
    }
    
    .temperature-control {
        flex-direction: column;
        align-items: stretch;
        gap: var(--ds-space-100, 8px);
    }
    
    .temperature-input {
        width: 100%;
    }
    
    .modal-content {
        margin: var(--ds-space-100, 8px);
        max-width: none;
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .modal-footer button {
        width: 100%;
    }
}

/* Confluence Configuration Styles */
.form-select {
    padding: var(--ds-space-150, 12px);
    border: 2px solid var(--ds-border-input, #DFE1E6);
    border-radius: var(--ds-border-radius-100, 6px);
    font-family: var(--ds-font-family-sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif);
    font-size: 14px;
    line-height: 1.4;
    background: var(--ds-surface, #FFFFFF);
    color: var(--ds-text, #172B4D);
    transition: border-color 0.2s ease;
    cursor: pointer;
}

.form-select:focus {
    outline: none;
    border-color: var(--ds-border-focused, #0052CC);
    box-shadow: 0 0 0 2px var(--ds-border-focused, rgba(0, 82, 204, 0.2));
}

.form-select:hover {
    border-color: var(--ds-border-bold, #C1C7D0);
}

.current-config {
    padding: var(--ds-space-150, 12px);
    background: var(--ds-background-success-subtle, #E3FCEF);
    border: 1px solid var(--ds-border-success, #00875A);
    border-radius: var(--ds-border-radius-100, 6px);
    margin-top: var(--ds-space-100, 8px);
}

.current-config p {
    margin: 0;
    color: var(--ds-text-success, #00875A);
    font-size: 14px;
    font-weight: 500;
}

.config-details {
    margin-top: var(--ds-space-050, 4px) !important;
    color: var(--ds-text-subtle, #42526E) !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    font-family: var(--ds-font-family-mono, monospace) !important;
}

/* List Management Styles */
.management-sections {
    margin-top: var(--ds-space-400, 32px);
    padding-top: var(--ds-space-300, 24px);
}

.management-section {
    margin-bottom: var(--ds-space-400, 32px);
    padding: var(--ds-space-300, 24px);
    background: var(--ds-background-neutral-subtle, #FAFBFC);
    border: 1px solid var(--ds-border, #DFE1E6);
    border-radius: var(--ds-border-radius-200, 8px);
}

.section-title {
    margin: 0 0 var(--ds-space-100, 8px) 0;
    color: var(--ds-text, #172B4D);
    font-size: 18px;
    font-weight: 500;
}

.section-description {
    margin: 0 0 var(--ds-space-200, 16px) 0;
    color: var(--ds-text-subtlest, #6B778C);
    font-size: 14px;
    line-height: 1.4;
}

.add-control {
    display: flex;
    gap: var(--ds-space-150, 12px);
    align-items: center;
    margin-bottom: var(--ds-space-200, 16px);
    flex-wrap: wrap;
}

.add-select {
    flex: 1;
    min-width: 200px;
}

.add-button {
    white-space: nowrap;
}

.configured-list {
    margin-top: var(--ds-space-200, 16px);
}

.list-title {
    margin: 0 0 var(--ds-space-150, 12px) 0;
    color: var(--ds-text, #172B4D);
    font-size: 16px;
    font-weight: 500;
}

.list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ds-space-150, 12px);
    margin-bottom: var(--ds-space-100, 8px);
    background: var(--ds-surface, #FFFFFF);
    border: 1px solid var(--ds-border, #DFE1E6);
    border-radius: var(--ds-border-radius-100, 6px);
    transition: box-shadow 0.2s ease;
}

.list-item:hover {
    box-shadow: var(--ds-shadow-raised, 0 1px 1px rgba(9, 30, 66, 0.25));
}

.item-name {
    color: var(--ds-text, #172B4D);
    font-size: 14px;
    font-weight: 500;
    flex: 1;
    margin-right: var(--ds-space-150, 12px);
}

.remove-button {
    background: var(--ds-background-danger-bold, #DE350B);
    color: var(--ds-text-inverse, white);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
    flex-shrink: 0;
}

.remove-button:hover {
    background-color: var(--ds-background-danger-bold-hovered, #FF5630);
}

.remove-button:active {
    background-color: var(--ds-background-danger-bold-pressed, #BF2600);
}

/* Clear All Configuration Section */
.clear-all-section {
    margin-top: var(--ds-space-400, 32px);
    padding-top: var(--ds-space-300, 24px);
    justify-content: flex-start;
}

/* Responsive design for list management */
@media (max-width: 768px) {
    .management-sections {
        margin-top: var(--ds-space-300, 24px);
        padding-top: var(--ds-space-200, 16px);
    }
    
    .management-section {
        padding: var(--ds-space-200, 16px);
        margin-bottom: var(--ds-space-300, 24px);
    }
    
    .add-control {
        flex-direction: column;
        align-items: stretch;
        gap: var(--ds-space-100, 8px);
    }
    
    .add-select {
        min-width: unset;
        width: 100%;
    }
    
    .add-button {
        width: 100%;
        justify-content: center;
        display: flex;
    }
    
    .list-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--ds-space-100, 8px);
    }
    
    .item-name {
        margin-right: 0;
        text-align: left;
    }
    
    .remove-button {
        align-self: flex-end;
    }
}