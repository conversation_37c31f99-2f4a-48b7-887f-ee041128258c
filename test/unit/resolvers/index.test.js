import { jest } from '@jest/globals';

// Test data  
const mockSimpleField = { id: 'customfield_10001', name: 'Prompt' };
const mockFullField = { id: 'customfield_10002', name: 'AI Requirements' };
const mockSummaryField = { id: 'summary', name: 'Summary' };

// Create mock functions for service-level mocking
const mockGetAllFieldsForProjectAndIssueType = jest.fn();
const mockUpdateIssueFields = jest.fn();
const mockIsFieldUpdated = jest.fn();
const mockGetIssue = jest.fn();
const mockQueuePush = jest.fn();
const mockAIServiceExpandRequirements = jest.fn();
const mockRequirementsServiceProcessRequirements = jest.fn();
const mockRequirementsServiceCreateLoadingMessage = jest.fn();
const mockResolveOutputFieldForIssueType = jest.fn();

// Mock Queue class
const mockQueue = {
  push: mockQueuePush
};
const mockQueueConstructor = jest.fn().mockReturnValue(mockQueue);

// Mock only the services at the service level

jest.unstable_mockModule('../../../src/services/index.js', () => {
  // Mock service classes
  class MockJiraService {
    constructor() {}
    async getIssue(issueId) { return mockGetIssue(issueId); }
    async getFieldsByName(fieldNames, projectId, issueTypeId) { 
      return mockGetAllFieldsForProjectAndIssueType(fieldNames, projectId, issueTypeId); 
    }
    async resolveOutputFieldForIssueType(issueTypeName, projectId, issueTypeId) {
      return mockResolveOutputFieldForIssueType(issueTypeName, projectId, issueTypeId);
    }
    isFieldUpdated(changelog, field) { return mockIsFieldUpdated(changelog, field); }
    extractFieldValueFromIssue(issue, field) {
      if (!issue || !issue.fields) return null;
      const value = issue.fields[field.id];
      return value && value.trim() ? value : null;
    }
    async updateIssueFields(issueId, fields) { return mockUpdateIssueFields(issueId, fields); }
  }
  
  class MockQueueService {
    constructor() {}
    async enqueueRequirementsProcessing(data) {
      await mockQueuePush([{ body: data }]);
      return 'test-job-id';
    }
  }
  
  class MockAIService {
    constructor() {}
    async expandRequirements(title, requirements, issueType) {
      return mockAIServiceExpandRequirements(title, requirements, issueType);
    }
  }
  
  class MockRequirementsService {
    constructor() {}
    processRequirements(requirements) {
      return mockRequirementsServiceProcessRequirements(requirements);
    }
    createLoadingMessage() {
      return mockRequirementsServiceCreateLoadingMessage();
    }
  }
  
  return {
    JiraService: MockJiraService,
    QueueService: MockQueueService,
    AIService: MockAIService,
    RequirementsService: MockRequirementsService
  };
});

// Import the module under test after mocking
const { promptUpdateHandler, requirementsProcessorHandler } = await import('../../../src/resolvers/index.js');
const mockIssue = {
  id: '10001',
  key: 'TEST-1',
  fields: {
    project: { id: '10000', key: 'TEST' },
    issuetype: { id: '10001', name: 'Task' },
    summary: 'Test Issue',
    'customfield_10001': 'Simple requirements text'
  }
};
const mockEvent = {
  issue: { id: '10001', key: 'TEST-1' },
  changelog: {
    items: [{ fieldId: 'customfield_10001', field: 'Prompt', from: 'old', to: 'new requirements' }]
  }
};
const mockEventOtherFieldUpdate = { 
  issue: { id: '10001', key: 'TEST-1' }, 
  changelog: { 
    items: [{ fieldId: 'description', field: 'Description', from: 'old desc', to: 'new desc' }] 
  } 
};
const mockEventCreation = {
  issue: {
    id: '10001',
    key: 'TEST-1',
    fields: {
      'customfield_10001': 'Create user login functionality'
    }
  }
  // No changelog indicates this is a creation event
};
const mockContext = { accountId: 'test-account-id' };

describe('promptUpdateHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockQueuePush.mockResolvedValue({ jobId: 'test-job-id' });
    mockUpdateIssueFields.mockResolvedValue(true);
    // Mock getIssue to return the mock issue
    mockGetIssue.mockResolvedValue(mockIssue);
    // Mock getFieldsByName to return the fields as an array (when multiple names are requested)
    mockGetAllFieldsForProjectAndIssueType.mockResolvedValue([
      mockSimpleField,
      mockSummaryField
    ]);
    // Mock resolveOutputFieldForIssueType to return the AI Requirements field by default
    mockResolveOutputFieldForIssueType.mockResolvedValue(mockFullField);
    // Mock createLoadingMessage to return ADF content
    mockRequirementsServiceCreateLoadingMessage.mockReturnValue({ 
      type: 'doc', 
      content: [{ type: 'paragraph', content: [{ type: 'text', marks: [{ type: 'em' }], text: 'Requirements are being generated...' }] }] 
    });
  });


  describe('Custom field resolution', () => {

    it('should return early if simple requirements field cannot be resolved', async () => {
      // Mock getFieldsByName to return fields but simple field is missing (null for first field)
      mockGetAllFieldsForProjectAndIssueType.mockResolvedValueOnce([
        null, // Prompt field not found
        mockSummaryField
      ]);

      await promptUpdateHandler(mockEvent, mockContext);

      expect(mockGetIssue).toHaveBeenCalledWith('10001');
      expect(mockGetAllFieldsForProjectAndIssueType).toHaveBeenCalledWith(['Prompt', 'Summary'], '10000', '10001');
      expect(mockResolveOutputFieldForIssueType).toHaveBeenCalledWith('Task', '10000', '10001');
      expect(mockIsFieldUpdated).not.toHaveBeenCalled();
    });

    it('should return early if full requirements field cannot be resolved', async () => {
      // Mock resolveOutputFieldForIssueType to return null (field not found)
      mockResolveOutputFieldForIssueType.mockResolvedValueOnce(null);

      await promptUpdateHandler(mockEvent, mockContext);

      expect(mockGetIssue).toHaveBeenCalledWith('10001');
      expect(mockGetAllFieldsForProjectAndIssueType).toHaveBeenCalledWith(['Prompt', 'Summary'], '10000', '10001');
      expect(mockResolveOutputFieldForIssueType).toHaveBeenCalledWith('Task', '10000', '10001');
      expect(mockIsFieldUpdated).not.toHaveBeenCalled();
    });

    it('should proceed if both fields are resolved successfully', async () => {
      // Use default mock setup from beforeEach which returns all fields
      mockIsFieldUpdated.mockReturnValue(false);

      await promptUpdateHandler(mockEvent, mockContext);

      expect(mockGetIssue).toHaveBeenCalledWith('10001');
      expect(mockGetAllFieldsForProjectAndIssueType).toHaveBeenCalledWith(['Prompt', 'Summary'], '10000', '10001');
      expect(mockResolveOutputFieldForIssueType).toHaveBeenCalledWith('Task', '10000', '10001');
      expect(mockIsFieldUpdated).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
    });
  });

  describe('Event type detection and field update validation', () => {
    beforeEach(() => {
      mockGetIssue.mockResolvedValue(mockIssue);
      mockGetAllFieldsForProjectAndIssueType.mockResolvedValue([
        mockSimpleField,
        mockSummaryField
      ]);
      mockResolveOutputFieldForIssueType.mockResolvedValue(mockFullField);
    });

    it('should process creation events (no changelog)', async () => {
      await promptUpdateHandler(mockEventCreation, mockContext);

      expect(mockGetIssue).toHaveBeenCalledWith('10001');
      expect(mockGetAllFieldsForProjectAndIssueType).toHaveBeenCalledWith(['Prompt', 'Summary'], '10000', '10001');
      expect(mockResolveOutputFieldForIssueType).toHaveBeenCalledWith('Task', '10000', '10001');
      expect(mockIsFieldUpdated).not.toHaveBeenCalled();
      expect(mockQueuePush).toHaveBeenCalled();
    });

    it('should return early if simple requirements field was not updated in update event', async () => {
      mockIsFieldUpdated.mockReturnValue(false);

      await promptUpdateHandler(mockEvent, mockContext);

      expect(mockGetIssue).toHaveBeenCalledWith('10001');
      expect(mockGetAllFieldsForProjectAndIssueType).toHaveBeenCalledWith(['Prompt', 'Summary'], '10000', '10001');
      expect(mockResolveOutputFieldForIssueType).toHaveBeenCalledWith('Task', '10000', '10001');
      expect(mockIsFieldUpdated).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockQueuePush).not.toHaveBeenCalled();
    });

    it('should proceed if simple requirements field was updated in update event', async () => {
      mockIsFieldUpdated.mockReturnValue(true);

      await promptUpdateHandler(mockEvent, mockContext);

      expect(mockIsFieldUpdated).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockQueuePush).toHaveBeenCalled();
    });
  });

  describe('Requirements processing', () => {
    beforeEach(() => {
      mockIsFieldUpdated.mockReturnValue(true);
    });

    it('should return early if simple requirements is empty', async () => {
      const mockIssueEmpty = {
        ...mockIssue,
        fields: {
          ...mockIssue.fields,
          'customfield_10001': ''
        }
      };
      mockGetIssue.mockResolvedValue(mockIssueEmpty);

      await promptUpdateHandler(mockEvent, mockContext);

      expect(mockGetIssue).toHaveBeenCalledWith('10001');
      expect(mockQueuePush).not.toHaveBeenCalled();
    });

    it('should return early if simple requirements is whitespace only', async () => {
      const mockIssueWhitespace = {
        ...mockIssue,
        fields: {
          ...mockIssue.fields,
          'customfield_10001': '   '
        }
      };
      mockGetIssue.mockResolvedValue(mockIssueWhitespace);

      await promptUpdateHandler(mockEvent, mockContext);

      expect(mockQueuePush).not.toHaveBeenCalled();
    });

    it('should enqueue processing if requirements are provided', async () => {
      const simpleRequirements = 'Create user login functionality';
      mockIsFieldUpdated.mockReturnValue(true);
      const mockIssueWithRequirements = {
        ...mockIssue,
        fields: {
          ...mockIssue.fields,
          'customfield_10001': simpleRequirements
        }
      };
      mockGetIssue.mockResolvedValue(mockIssueWithRequirements);

      await promptUpdateHandler(mockEvent, mockContext);

      expect(mockGetIssue).toHaveBeenCalledWith('10001');
      expect(mockQueuePush).toHaveBeenCalledWith([{
        body: {
          issue: mockIssueWithRequirements,
          simpleRequirements,
          fullReqFieldId: mockFullField.id,
          fullReqFieldName: mockFullField.name
        }
      }]);
    });

    it('should handle queue errors gracefully', async () => {
      const simpleRequirements = 'Create user login functionality';
      mockIsFieldUpdated.mockReturnValue(true);
      const mockIssueWithRequirements = {
        ...mockIssue,
        fields: {
          ...mockIssue.fields,
          'customfield_10001': simpleRequirements
        }
      };
      mockGetIssue.mockResolvedValue(mockIssueWithRequirements);
      mockQueuePush.mockRejectedValue(new Error('Queue failed'));

      await promptUpdateHandler(mockEvent, mockContext);

      expect(mockQueuePush).toHaveBeenCalled();
      // Should not throw - error is handled gracefully
    });
  });

  describe('Complete workflow', () => {
    it('should execute complete successful enqueue workflow', async () => {
      const simpleRequirements = 'Create user login functionality';
      
      // Setup all mocks for successful execution
      mockIsFieldUpdated.mockReturnValue(true);
      const mockIssueWithRequirements = {
        ...mockIssue,
        fields: {
          ...mockIssue.fields,
          'customfield_10001': simpleRequirements
        }
      };
      mockGetIssue.mockResolvedValue(mockIssueWithRequirements);

      await promptUpdateHandler(mockEvent, mockContext);

      // Verify all steps were executed in correct order
      expect(mockGetIssue).toHaveBeenCalledWith('10001');
      expect(mockGetAllFieldsForProjectAndIssueType).toHaveBeenCalledWith(['Prompt', 'Summary'], '10000', '10001');
      expect(mockResolveOutputFieldForIssueType).toHaveBeenCalledWith('Task', '10000', '10001');
      expect(mockIsFieldUpdated).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockQueuePush).toHaveBeenCalledWith([{
        body: {
          issue: mockIssueWithRequirements,
          simpleRequirements,
          fullReqFieldId: mockFullField.id,
          fullReqFieldName: mockFullField.name
        }
      }]);
    });
  });

  describe('Issue Creation Events', () => {
    const mockCreationEvent = {
      issue: {
        id: '10001',
        key: 'TEST-1',
        fields: {
          'customfield_10001': 'Create user login functionality'
        }
      }
      // No changelog indicates this is a creation event
    };

    it('should process issue creation with simple requirements', async () => {
      const mockCreationIssue = {
        id: '10001',
        key: 'TEST-1',
        fields: {
          project: { id: '10000', key: 'TEST' },
          issuetype: { id: '10001', name: 'Task' },
          'customfield_10001': 'Create user login functionality',
          'summary': 'Test Issue Title'
        }
      };
      
      mockGetIssue.mockResolvedValue(mockCreationIssue);
      mockGetAllFieldsForProjectAndIssueType.mockResolvedValue([
        mockSimpleField,
        mockSummaryField
      ]);
      mockResolveOutputFieldForIssueType.mockResolvedValue(mockFullField);
      
      await promptUpdateHandler(mockCreationEvent, mockContext);

      // Verify that getIssue was called
      expect(mockGetIssue).toHaveBeenCalledWith('10001');
      expect(mockGetAllFieldsForProjectAndIssueType).toHaveBeenCalledWith(['Prompt', 'Summary'], '10000', '10001');
      expect(mockResolveOutputFieldForIssueType).toHaveBeenCalledWith('Task', '10000', '10001');
      expect(mockQueuePush).toHaveBeenCalledWith([{
        body: {
          issue: mockCreationIssue,
          simpleRequirements: 'Create user login functionality',
          fullReqFieldId: mockFullField.id,
          fullReqFieldName: mockFullField.name
        }
      }]);
    });

    it('should skip processing when simple requirements field is empty on creation', async () => {
      const mockCreationIssue = {
        id: '10001',
        key: 'TEST-1',
        fields: {
          project: { id: '10000', key: 'TEST' },
          issuetype: { id: '10001', name: 'Task' },
          'customfield_10001': ''
        }
      };
      
      mockGetIssue.mockResolvedValue(mockCreationIssue);
      mockGetAllFieldsForProjectAndIssueType.mockResolvedValue([
        mockSimpleField,
        mockSummaryField
      ]);
      mockResolveOutputFieldForIssueType.mockResolvedValue(mockFullField);

      await promptUpdateHandler(mockCreationEvent, mockContext);

      expect(mockGetIssue).toHaveBeenCalledWith('10001');
      expect(mockGetAllFieldsForProjectAndIssueType).toHaveBeenCalledWith(['Prompt', 'Summary'], '10000', '10001');
      expect(mockResolveOutputFieldForIssueType).toHaveBeenCalledWith('Task', '10000', '10001');
      expect(mockQueuePush).not.toHaveBeenCalled();
    });

    it('should skip processing when simple requirements field is missing on creation', async () => {
      const mockCreationIssue = {
        id: '10001',
        key: 'TEST-1',
        fields: {
          project: { id: '10000', key: 'TEST' },
          issuetype: { id: '10001', name: 'Task' }
        }
      };
      
      mockGetIssue.mockResolvedValue(mockCreationIssue);
      mockGetAllFieldsForProjectAndIssueType.mockResolvedValue([
        mockSimpleField,
        mockSummaryField
      ]);
      mockResolveOutputFieldForIssueType.mockResolvedValue(mockFullField);

      await promptUpdateHandler(mockCreationEvent, mockContext);

      expect(mockGetIssue).toHaveBeenCalledWith('10001');
      expect(mockGetAllFieldsForProjectAndIssueType).toHaveBeenCalledWith(['Prompt', 'Summary'], '10000', '10001');
      expect(mockResolveOutputFieldForIssueType).toHaveBeenCalledWith('Task', '10000', '10001');
      expect(mockQueuePush).not.toHaveBeenCalled();
    });
  });
});

describe('requirementsProcessorHandler', () => {
  const mockTitle = 'User Login Feature';
  const mockRequirements = 'Create user login functionality';
  const mockProcessorEvent = {
    call: {
      payload: {
        body: {
          issue: {
            id: '10001',
            key: 'TEST-1',
            fields: {
              summary: mockTitle,
              issuetype: {
                name: 'Story'
              }
            }
          },
          simpleRequirements: mockRequirements,
          fullReqFieldId: 'customfield_10002'
        }
      }
    }
  };
  const mockContext = { accountId: 'test-account-id' };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AI Processing', () => {
    it('should create prompt and call OpenRouter API successfully', async () => {
      const mockExpandedRequirements = '# Expanded Requirements\n\nDetailed requirements here.';
      const mockAdfContent = { type: 'doc', content: [] };

      mockAIServiceExpandRequirements.mockResolvedValue(mockExpandedRequirements);
      mockRequirementsServiceProcessRequirements.mockReturnValue(mockAdfContent);
      mockUpdateIssueFields.mockResolvedValue(true);

      await requirementsProcessorHandler(mockProcessorEvent, mockContext);

      expect(mockAIServiceExpandRequirements).toHaveBeenCalledWith(mockTitle, mockRequirements, 'story');
      expect(mockRequirementsServiceProcessRequirements).toHaveBeenCalledWith(mockExpandedRequirements);
      expect(mockUpdateIssueFields).toHaveBeenCalledWith('10001', {
        'customfield_10002': mockAdfContent
      });
    });

    it('should throw error if OpenRouter API fails', async () => {
      mockAIServiceExpandRequirements.mockResolvedValue(null);

      await expect(requirementsProcessorHandler(mockProcessorEvent, mockContext))
        .rejects.toThrow('AI service failed to expand requirements');

      expect(mockRequirementsServiceProcessRequirements).not.toHaveBeenCalled();
      expect(mockUpdateIssueFields).not.toHaveBeenCalled();
    });

    it('should throw error if issue update fails', async () => {
      const mockExpandedRequirements = '# Expanded Requirements\n\nDetailed requirements here.';
      const mockAdfContent = { type: 'doc', content: [] };

      mockAIServiceExpandRequirements.mockResolvedValue(mockExpandedRequirements);
      mockRequirementsServiceProcessRequirements.mockReturnValue(mockAdfContent);
      mockUpdateIssueFields.mockResolvedValue(false);

      await expect(requirementsProcessorHandler(mockProcessorEvent, mockContext))
        .rejects.toThrow('Failed to update Jira issue with expanded requirements');
    });
  });
});