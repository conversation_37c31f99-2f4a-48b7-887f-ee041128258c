/**
 * Unit tests for ConfluenceService
 */

import { jest } from '@jest/globals';

// Mock confluence-helpers
jest.unstable_mockModule('../../../src/utils/confluence-helpers.js', () => ({
  getSpaces: jest.fn(),
  getPagesInSpace: jest.fn(),
  getPageContent: jest.fn(),
  searchPagesByTitle: jest.fn(),
  getAllPagesFromSpaces: jest.fn(),
  createPage: jest.fn(),
  updatePage: jest.fn(),
  getSpace: jest.fn()
}));

// Mock KVS
jest.unstable_mockModule('@forge/kvs', () => ({
  kvs: {
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn()
  }
}));

// Mock adf-to-md
jest.unstable_mockModule('adf-to-md', () => ({
  convert: jest.fn().mockReturnValue({ result: 'converted markdown' })
}));

// Mock AIService for circular dependency in getProjectContext
jest.unstable_mockModule('../../../src/services/AIService.js', () => ({
  AIService: jest.fn().mockImplementation(() => ({
    analyzePageRelevancy: jest.fn().mockResolvedValue(['123', '456'])
  }))
}));

// Import after mocking
const { ConfluenceService } = await import('../../../src/services/ConfluenceService.js');
const {
  getSpaces,
  getAllPagesFromSpace,
  getPageContent,
  searchPagesByTitle,
  getAllPagesFromSpaces
} = await import('../../../src/utils/confluence-helpers.js');
const { kvs } = await import('@forge/kvs');

describe('ConfluenceService', () => {
  let confluenceService;

  beforeEach(() => {
    confluenceService = new ConfluenceService();

    // Reset all mocks
    jest.clearAllMocks();

    kvs.get.mockResolvedValue(null);
    kvs.set.mockResolvedValue(undefined);
    kvs.delete.mockResolvedValue(undefined);
  });


  describe('getSpaces', () => {
    it('should call helper function and return result', async () => {
      const mockSpaces = [
        { key: 'SPACE1', name: 'Space 1' },
        { key: 'SPACE2', name: 'Space 2' }
      ];

      getSpaces.mockResolvedValue(mockSpaces);

      const result = await confluenceService.getSpaces();

      expect(getSpaces).toHaveBeenCalledWith();
      expect(result).toEqual(mockSpaces);
    });
  });

  describe('getPagesInSpace', () => {
    it('should call helper function with correct parameters', async () => {
      const mockPages = [
        { id: '123', title: 'Page 1' },
        { id: '456', title: 'Page 2' }
      ];

      getPagesInSpace.mockResolvedValue(mockPages);

      const result = await confluenceService.getPagesInSpace('TEST', 10);

      expect(getPagesInSpace).toHaveBeenCalledWith('TEST', 10);
      expect(result).toEqual(mockPages);
    });

    it('should use default limit when not specified', async () => {
      getPagesInSpace.mockResolvedValue([]);

      await confluenceService.getPagesInSpace('TEST');

      expect(getPagesInSpace).toHaveBeenCalledWith('TEST', 25);
    });
  });

  describe('getPageContent', () => {
    const mockPageData = {
      id: '123',
      title: 'Test Page',
      body: {
        view: {
          value: '<p>Test content</p>'
        }
      },
      version: { number: 1 }
    };

    it('should fetch page content successfully without cache', async () => {
      getPageContent.mockResolvedValue(mockPageData);

      const result = await confluenceService.getPageContent('123', 'project-1', false);

      expect(getPageContent).toHaveBeenCalledWith('123');
      expect(result).toEqual(mockPageData);
      expect(kvs.get).not.toHaveBeenCalled();
    });

    it('should check cache first when useCache is true', async () => {
      const cachedData = {
        data: mockPageData,
        timestamp: Date.now() - 1000, // 1 second ago
        version: 1
      };

      kvs.get.mockResolvedValue(cachedData);

      // Mock isCacheValid to return true
      confluenceService.isCacheValid = jest.fn().mockReturnValue(true);

      const result = await confluenceService.getPageContent('123', 'project-1', true);

      expect(kvs.get).toHaveBeenCalledWith('confluence-cache-project-project-1-page-123');
      expect(result).toEqual(mockPageData);
      expect(getPageContent).not.toHaveBeenCalled();
    });

    it('should fetch from API when cache is invalid', async () => {
      const staleData = {
        data: mockPageData,
        timestamp: Date.now() - 100000000, // very old
        version: 1
      };

      kvs.get.mockResolvedValue(staleData);
      getPageContent.mockResolvedValue(mockPageData);

      const result = await confluenceService.getPageContent('123', 'project-1', true);

      expect(getPageContent).toHaveBeenCalledWith('123');
      expect(result).toEqual(mockPageData);
    });

    it('should return null when helper returns null', async () => {
      getPageContent.mockResolvedValue(null);

      const result = await confluenceService.getPageContent('123', 'project-1', false);

      expect(result).toBeNull();
    });
  });

  describe('extractPlainTextContent', () => {
    it('should extract plain text from HTML content', () => {
      const pageData = {
        body: {
          view: {
            value: '<h1>Title</h1><p>Some content</p><br><div>More content</div>'
          }
        }
      };

      const result = confluenceService.extractPlainTextContent(pageData);

      expect(result).toBe('Title\n\nSome content\n\n\nMore content');
    });

    it('should handle missing body data', () => {
      const result = confluenceService.extractPlainTextContent({});
      expect(result).toBe('');
    });

    it('should handle null page data', () => {
      const result = confluenceService.extractPlainTextContent(null);
      expect(result).toBe('');
    });

    it('should decode HTML entities', () => {
      const pageData = {
        body: {
          view: {
            value: '<p>&amp; &lt; &gt; &quot; &nbsp;</p>'
          }
        }
      };

      const result = confluenceService.extractPlainTextContent(pageData);
      expect(result).toBe('& < > "');
    });
  });

  describe('getProjectContext', () => {
    beforeEach(() => {
      // Mock both getConfiguredPages and getConfiguredSpaces to return empty arrays
      kvs.get.mockImplementation((key) => {
        if (key.includes('confluence-pages-project-')) return Promise.resolve([]);
        if (key.includes('confluence-spaces-project-')) return Promise.resolve([]);
        return Promise.resolve(null);
      });
    });

    it('should return empty string when no pages or spaces are configured', async () => {
      const result = await confluenceService.getProjectContext('project-1', 'Test Issue', 'Test prompt', 'Story');
      
      expect(result).toBe('');
    });

    it('should fetch and extract content from configured mandatory pages', async () => {
      const mockPageData = {
        body: {
          view: {
            value: '<h1>Project Context</h1><p>Important context</p>'
          }
        },
        version: { number: 1 }
      };

      // Mock configured pages (mandatory)
      kvs.get.mockImplementation((key) => {
        if (key.includes('confluence-pages-project-')) {
          return Promise.resolve([
            { pageId: 'page-123', title: 'Test Page', spaceKey: 'TEST' }
          ]);
        }
        if (key.includes('confluence-spaces-project-')) return Promise.resolve([]);
        return Promise.resolve(null);
      });
      
      getPageContent.mockResolvedValue(mockPageData);

      const result = await confluenceService.getProjectContext('project-1', 'Test Issue', 'Test prompt', 'Story');

      expect(result).toContain('Test Page');
      expect(result).toContain('Project Context\n\nImportant context');
    });

    it('should combine mandatory pages with AI-recommended space pages', async () => {
      const mockPageData = {
        body: {
          view: {
            value: '<p>Page content</p>'
          }
        },
        version: { number: 1 }
      };

      // Mock configured mandatory pages and spaces
      kvs.get.mockImplementation((key) => {
        if (key.includes('confluence-pages-project-')) {
          return Promise.resolve([
            { pageId: 'page-123', title: 'Mandatory Page', spaceKey: 'TEST' }
          ]);
        }
        if (key.includes('confluence-spaces-project-')) {
          return Promise.resolve([
            { key: 'SPACE1', name: 'Test Space' }
          ]);
        }
        return Promise.resolve(null);
      });
      
      // Mock getAllPagesFromSpaces to return pages from spaces
      getAllPagesFromSpaces.mockResolvedValue([
        { pageId: 'page-456', title: 'Space Page 1', spaceKey: 'SPACE1' },
        { pageId: 'page-789', title: 'Space Page 2', spaceKey: 'SPACE1' }
      ]);
      
      getPageContent.mockResolvedValue(mockPageData);

      const result = await confluenceService.getProjectContext('project-1', 'Test Issue', 'Test prompt', 'Story');

      expect(getAllPagesFromSpaces).toHaveBeenCalledWith(['SPACE1'], 25);
      expect(result.length).toBeGreaterThan(0);
      expect(result).toContain('Mandatory Page');
    });

    it('should handle errors gracefully and return empty string', async () => {
      kvs.get.mockRejectedValue(new Error('Storage error'));

      const result = await confluenceService.getProjectContext('project-1', 'Test Issue', 'Test prompt', 'Story');

      expect(result).toBe('');
    });
  });

  describe('setConfiguredPageId', () => {
    it('should set configured page ID successfully', async () => {
      kvs.set.mockResolvedValue(undefined);

      const result = await confluenceService.setConfiguredPageId('project-1', 'page-123');
      
      expect(kvs.set).toHaveBeenCalledWith('confluence-page-project-project-1', 'page-123');
      expect(result).toBe(true);
    });

    it('should handle storage errors', async () => {
      kvs.set.mockRejectedValue(new Error('Storage error'));

      const result = await confluenceService.setConfiguredPageId('project-1', 'page-123');
      
      expect(result).toBe(false);
    });
  });

  describe('getConfiguredPageId', () => {
    it('should get configured page ID successfully', async () => {
      kvs.get.mockResolvedValue('page-123');

      const result = await confluenceService.getConfiguredPageId('project-1');
      
      expect(kvs.get).toHaveBeenCalledWith('confluence-page-project-project-1');
      expect(result).toBe('page-123');
    });

    it('should return null when no page is configured', async () => {
      kvs.get.mockResolvedValue(null);

      const result = await confluenceService.getConfiguredPageId('project-1');
      
      expect(result).toBeNull();
    });

    it('should handle storage errors', async () => {
      kvs.get.mockRejectedValue(new Error('Storage error'));

      const result = await confluenceService.getConfiguredPageId('project-1');
      
      expect(result).toBeNull();
    });
  });

  describe('isCacheValid', () => {
    it('should return true for valid cache', () => {
      const recentTime = Date.now() - 1000; // 1 second ago
      const cachedContent = {
        timestamp: recentTime,
        data: {}
      };

      const result = confluenceService.isCacheValid(cachedContent);
      expect(result).toBe(true);
    });

    it('should return false for expired cache', () => {
      const oldTime = Date.now() - (confluenceService.defaultCacheTTL + 1000);
      const cachedContent = {
        timestamp: oldTime,
        data: {}
      };

      const result = confluenceService.isCacheValid(cachedContent);
      expect(result).toBe(false);
    });

    it('should return false for cache without timestamp', () => {
      const cachedContent = { data: {} };

      const result = confluenceService.isCacheValid(cachedContent);
      expect(result).toBe(false);
    });

    it('should return false for null cache', () => {
      const result = confluenceService.isCacheValid(null);
      expect(result).toBe(false);
    });
  });

  describe('searchPagesByTitle', () => {
    it('should call helper function with correct parameters', async () => {
      const mockPages = [
        { id: '123', title: 'Test Page' }
      ];

      searchPagesByTitle.mockResolvedValue(mockPages);

      const result = await confluenceService.searchPagesByTitle('Test Page', 5);

      expect(searchPagesByTitle).toHaveBeenCalledWith('Test Page', 5);
      expect(result).toEqual(mockPages);
    });

    it('should use default limit when not specified', async () => {
      searchPagesByTitle.mockResolvedValue([]);

      await confluenceService.searchPagesByTitle('Test');

      expect(searchPagesByTitle).toHaveBeenCalledWith('Test', 10);
    });
  });
});