import { jest } from '@jest/globals';

// Mock marklassian before importing RequirementsService
jest.unstable_mockModule('marklassian', () => ({
  markdownToAdf: jest.fn()
}));

// Import after mocking
const { RequirementsService } = await import('../../../src/services/RequirementsService.js');
const { markdownToAdf } = await import('marklassian');

describe('RequirementsService', () => {
  let service;

  beforeEach(() => {
    service = new RequirementsService();
    jest.clearAllMocks();
  });

  describe('validateRequirements', () => {
    it('should return true for valid requirements', () => {
      const result = service.validateRequirements('Valid requirements text');
      expect(result).toBe(true);
    });

    it('should return false for empty string', () => {
      const result = service.validateRequirements('');
      expect(result).toBe(false);
    });

    it('should return false for whitespace only', () => {
      const result = service.validateRequirements('   ');
      expect(result).toBe(false);
    });

    it('should return false for null', () => {
      const result = service.validateRequirements(null);
      expect(result).toBe(false);
    });

    it('should return false for undefined', () => {
      const result = service.validateRequirements(undefined);
      expect(result).toBe(false);
    });

    it('should return false for non-string', () => {
      const result = service.validateRequirements(123);
      expect(result).toBe(false);
    });
  });

  describe('cleanMarkdownResponse', () => {
    it('should return content unchanged if no code block wrapping', () => {
      const content = `# Test Content

This is regular markdown content without code block wrapping.

## Section
- Item 1
- Item 2`;

      const result = service.cleanMarkdownResponse(content);
      expect(result).toBe(content);
    });

    it('should remove markdown code block wrapping with "markdown" language specifier', () => {
      const wrappedContent = `\`\`\`markdown
# Test Content

This is content wrapped in a markdown code block.

## Section
- Item 1
- Item 2
\`\`\``;

      const expectedContent = `# Test Content

This is content wrapped in a markdown code block.

## Section
- Item 1
- Item 2`;

      const result = service.cleanMarkdownResponse(wrappedContent);
      expect(result).toBe(expectedContent);
    });

    it('should remove code block wrapping without language specifier', () => {
      const wrappedContent = `\`\`\`
# Test Content

This is content wrapped in a code block without language specifier.

## Section
- Item 1
- Item 2
\`\`\``;

      const expectedContent = `# Test Content

This is content wrapped in a code block without language specifier.

## Section
- Item 1
- Item 2`;

      const result = service.cleanMarkdownResponse(wrappedContent);
      expect(result).toBe(expectedContent);
    });

    it('should handle empty content', () => {
      expect(service.cleanMarkdownResponse('')).toBe('');
    });

    it('should handle null content', () => {
      expect(service.cleanMarkdownResponse(null)).toBeNull();
    });

    it('should handle undefined content', () => {
      expect(service.cleanMarkdownResponse(undefined)).toBeUndefined();
    });

    it('should not remove code blocks that are not wrapping the entire content', () => {
      const content = `# Test Content

Here is some text with a code block in the middle:

\`\`\`javascript
console.log('Hello World');
\`\`\`

And more text after.`;

      const result = service.cleanMarkdownResponse(content);
      expect(result).toBe(content);
    });

    it('should handle content with extra whitespace around code blocks', () => {
      const wrappedContent = `\`\`\`markdown
# Test Content

This has extra whitespace around the code block markers.
\`\`\``;

      const expectedContent = `# Test Content

This has extra whitespace around the code block markers.`;

      const result = service.cleanMarkdownResponse(wrappedContent);
      expect(result).toBe(expectedContent);
    });

    it('should handle single line content wrapped in code blocks', () => {
      const wrappedContent = `\`\`\`markdown
# Single Line Title
\`\`\``;

      const expectedContent = `# Single Line Title`;

      const result = service.cleanMarkdownResponse(wrappedContent);
      expect(result).toBe(expectedContent);
    });

    it('should not remove partial code block markers', () => {
      const content = `# Test Content

This content has partial markers \`\`\` but not complete wrapping.

## Section
- Item 1
- Item 2`;

      const result = service.cleanMarkdownResponse(content);
      expect(result).toBe(content);
    });

    it('should handle content with multiple separate code blocks', () => {
      const content = `# Test Content

Here is the first code block:
\`\`\`javascript
console.log('First block');
\`\`\`

And here is the second code block:
\`\`\`python
print('Second block')
\`\`\`

This should not be modified.`;

      const result = service.cleanMarkdownResponse(content);
      expect(result).toBe(content);
    });
  });

  describe('processRequirements', () => {
    const mockAdfContent = { type: 'doc', content: [] };

    beforeEach(() => {
      markdownToAdf.mockReturnValue(mockAdfContent);
    });

    it('should process valid requirements successfully', () => {
      const requirements = 'Valid requirements text';
      
      const result = service.processRequirements(requirements);
      
      expect(result).toBe(mockAdfContent);      
      expect(markdownToAdf).toHaveBeenCalledWith(requirements);
    });

    it('should clean markdown code blocks before converting to ADF', () => {
      const wrappedRequirements = `\`\`\`markdown
# Test Requirements

This is wrapped in a code block.
\`\`\``;

      const cleanedRequirements = `# Test Requirements

This is wrapped in a code block.`;

      const result = service.processRequirements(wrappedRequirements);
      
      expect(result).toBe(mockAdfContent);
      expect(markdownToAdf).toHaveBeenCalledWith(cleanedRequirements);
    });

    it('should throw error for invalid requirements', () => {
      expect(() => service.processRequirements('')).toThrow('Invalid requirements: must be a non-empty string');
      expect(() => service.processRequirements(null)).toThrow('Invalid requirements: must be a non-empty string');
      expect(() => service.processRequirements(undefined)).toThrow('Invalid requirements: must be a non-empty string');
    });

    it('should validate, clean, and convert in correct order', () => {
      const spy = jest.spyOn(service, 'validateRequirements');
      const cleanSpy = jest.spyOn(service, 'cleanMarkdownResponse');
      
      const requirements = 'Valid requirements';
      service.processRequirements(requirements);
      
      expect(spy).toHaveBeenCalledWith(requirements);
      expect(cleanSpy).toHaveBeenCalledWith(requirements);
      expect(markdownToAdf).toHaveBeenCalled();
    });
  });

  describe('createLoadingMessage', () => {
    const mockAdfContent = { type: 'doc', content: [] };

    beforeEach(() => {
      markdownToAdf.mockReturnValue(mockAdfContent);
    });

    it('should create loading message in italic ADF format', () => {
      const result = service.createLoadingMessage();
      
      expect(result).toBe(mockAdfContent);
      expect(markdownToAdf).toHaveBeenCalledWith('*Requirements are being generated...*');
    });
  });
});